{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-113:/values-nb/values-nb.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,87", "endOffsets": "140,228"}, "to": {"startLines": "514,515", "startColumns": "4,4", "startOffsets": "48596,48686", "endColumns": "89,87", "endOffsets": "48681,48769"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\587767e501a9ab66a3f91617d285250f\\transformed\\core-1.16.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "40,41,42,43,44,45,46,196", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3576,3670,3772,3869,3968,4076,4182,18197", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "3665,3767,3864,3963,4071,4177,4297,18293"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-nb\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "83", "startColumns": "4", "startOffsets": "8274", "endColumns": "129", "endOffsets": "8399"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,279,376,476,564,640,728,817,899,963,1027,1107,1189,1259,1336,1403", "endColumns": "92,80,96,99,87,75,87,88,81,63,63,79,81,69,76,66,119", "endOffsets": "193,274,371,471,559,635,723,812,894,958,1022,1102,1184,1254,1331,1398,1518"}, "to": {"startLines": "50,51,96,98,100,121,122,182,183,185,186,189,190,194,511,512,513", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4602,4695,9765,9950,10126,12345,12421,17054,17143,17303,17367,17651,17731,18055,48332,48409,48476", "endColumns": "92,80,96,99,87,75,87,88,81,63,63,79,81,69,76,66,119", "endOffsets": "4690,4771,9857,10045,10209,12416,12504,17138,17220,17362,17426,17726,17808,18120,48404,48471,48591"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,215,294,387,473,551,628,749,842,941,1030,1150,1209,1330,1394,1571,1759,1899,1990,2093,2186,2287,2454,2562,2661,2891,2997,3123,3319,3533,3629,3742,3931,4025,4084,4148,4235,4332,4428,4505,4605,4694,4805,5029,5098,5165,5239,5346,5423,5512,5578,5659,5730,5806,5919,6005,6113,6203,6274,6362,6429,6515,6574,6676,6786,6891,7019,7080,7171,7267,7392,7463,7550,7651,7705,7763,7839,7945,8108,8377,8666,8762,8832,8919,8991,9097,9211,9298,9372,9441,9525,9609,9700,9821,9950,10023,10097,10149,10312,10381,10440,10514,10588,10666,10747,10888,11017,11131,11217,11292,11377,11445", "endColumns": "70,88,78,92,85,77,76,120,92,98,88,119,58,120,63,176,187,139,90,102,92,100,166,107,98,229,105,125,195,213,95,112,188,93,58,63,86,96,95,76,99,88,110,223,68,66,73,106,76,88,65,80,70,75,112,85,107,89,70,87,66,85,58,101,109,104,127,60,90,95,124,70,86,100,53,57,75,105,162,268,288,95,69,86,71,105,113,86,73,68,83,83,90,120,128,72,73,51,162,68,58,73,73,77,80,140,128,113,85,74,84,67,155", "endOffsets": "121,210,289,382,468,546,623,744,837,936,1025,1145,1204,1325,1389,1566,1754,1894,1985,2088,2181,2282,2449,2557,2656,2886,2992,3118,3314,3528,3624,3737,3926,4020,4079,4143,4230,4327,4423,4500,4600,4689,4800,5024,5093,5160,5234,5341,5418,5507,5573,5654,5725,5801,5914,6000,6108,6198,6269,6357,6424,6510,6569,6671,6781,6886,7014,7075,7166,7262,7387,7458,7545,7646,7700,7758,7834,7940,8103,8372,8661,8757,8827,8914,8986,9092,9206,9293,9367,9436,9520,9604,9695,9816,9945,10018,10092,10144,10307,10376,10435,10509,10583,10661,10742,10883,11012,11126,11212,11287,11372,11440,11596"}, "to": {"startLines": "204,205,206,276,288,289,290,307,320,322,323,353,371,373,376,380,381,382,385,387,389,390,391,392,393,394,395,396,397,398,399,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,448,452,462,463,464,465,466,467,468,469,470,479,480,481,482,483,484,485,486,487,488,489,490,491,492,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18767,18838,18927,24489,26266,26352,26430,28360,29564,29703,29802,32253,33976,34096,34339,34632,34809,34997,35288,35479,35675,35768,35869,36036,36144,36243,36473,36579,36705,36901,37115,37292,37405,37594,37688,37747,37811,37898,37995,38091,38168,38268,38357,38468,38692,38761,38828,38902,39009,39086,39175,39241,39322,39393,39469,40019,40105,40213,40303,40374,40462,40529,40615,40674,40776,40886,40991,41119,41180,41271,41367,41492,41740,42060,43603,43657,43715,43791,43897,44060,44329,44618,44714,45314,45401,45473,45579,45693,45780,45854,45923,46007,46091,46182,46303,46432,46505,46659,46711,46874,46943,47002,47076,47150,47228,47309,47450,47579,47693,47779,47854,47939,48007", "endColumns": "70,88,78,92,85,77,76,120,92,98,88,119,58,120,63,176,187,139,90,102,92,100,166,107,98,229,105,125,195,213,95,112,188,93,58,63,86,96,95,76,99,88,110,223,68,66,73,106,76,88,65,80,70,75,112,85,107,89,70,87,66,85,58,101,109,104,127,60,90,95,124,70,86,100,53,57,75,105,162,268,288,95,69,86,71,105,113,86,73,68,83,83,90,120,128,72,73,51,162,68,58,73,73,77,80,140,128,113,85,74,84,67,155", "endOffsets": "18833,18922,19001,24577,26347,26425,26502,28476,29652,29797,29886,32368,34030,34212,34398,34804,34992,35132,35374,35577,35763,35864,36031,36139,36238,36468,36574,36700,36896,37110,37206,37400,37589,37683,37742,37806,37893,37990,38086,38163,38263,38352,38463,38687,38756,38823,38897,39004,39081,39170,39236,39317,39388,39464,39577,40100,40208,40298,40369,40457,40524,40610,40669,40771,40881,40986,41114,41175,41266,41362,41487,41558,41822,42156,43652,43710,43786,43892,44055,44324,44613,44709,44779,45396,45468,45574,45688,45775,45849,45918,46002,46086,46177,46298,46427,46500,46574,46706,46869,46938,46997,47071,47145,47223,47304,47445,47574,47688,47774,47849,47934,48002,48158"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e3c1b17a9ffaafd0471e6527f4794f6b\\transformed\\jetified-credentials-1.5.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,116", "endOffsets": "161,278"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "2943,3054", "endColumns": "110,116", "endOffsets": "3049,3166"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-nb\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,450,572,677,829,955,1071,1170,1320,1423,1580,1704,1842,2014,2077,2135", "endColumns": "101,154,121,104,151,125,115,98,149,102,156,123,137,171,62,57,73", "endOffsets": "294,449,571,676,828,954,1070,1169,1319,1422,1579,1703,1841,2013,2076,2134,2208"}, "to": {"startLines": "75,76,77,78,79,80,81,82,84,85,86,87,88,89,90,91,92", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7265,7371,7530,7656,7765,7921,8051,8171,8404,8558,8665,8826,8954,9096,9272,9339,9401", "endColumns": "105,158,125,108,155,129,119,102,153,106,160,127,141,175,66,61,77", "endOffsets": "7366,7525,7651,7760,7916,8046,8166,8269,8553,8660,8821,8949,9091,9267,9334,9396,9474"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,322,417,614,662,729,826,895,1124,1189,1287,1353,1408,1472,1546,1636,1931,2005,2071,2124,2177,2269,2395,2502,2559,2643,2719,2802,2867,2966,3242,3319,3396,3457,3517,3579,3639,3710,3790,3890,3983,4064,4167,4264,4337,4416,4501,4678,4870,4988,5117,5173,5833,5932,5997", "endColumns": "112,153,94,196,47,66,96,68,228,64,97,65,54,63,73,89,294,73,65,52,52,91,125,106,56,83,75,82,64,98,275,76,76,60,59,61,59,70,79,99,92,80,102,96,72,78,84,176,191,117,128,55,659,98,64,54", "endOffsets": "163,317,412,609,657,724,821,890,1119,1184,1282,1348,1403,1467,1541,1631,1926,2000,2066,2119,2172,2264,2390,2497,2554,2638,2714,2797,2862,2961,3237,3314,3391,3452,3512,3574,3634,3705,3785,3885,3978,4059,4162,4259,4332,4411,4496,4673,4865,4983,5112,5168,5828,5927,5992,6047"}, "to": {"startLines": "273,274,275,277,281,282,283,284,285,286,287,303,306,308,312,313,318,324,325,333,343,346,347,348,349,350,356,359,364,366,367,368,369,372,374,375,379,383,384,386,388,400,425,426,427,428,429,447,454,455,456,457,459,460,461,478", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24127,24240,24394,24582,25593,25641,25708,25805,25874,26103,26168,28054,28305,28481,28777,28851,29221,29891,29965,30494,31403,31607,31699,31825,31932,31989,32548,32786,33203,33345,33444,33720,33797,34035,34217,34277,34572,35137,35208,35379,35582,37211,39582,39685,39782,39855,39934,41563,42215,42407,42525,42654,42779,43439,43538,45259", "endColumns": "112,153,94,196,47,66,96,68,228,64,97,65,54,63,73,89,294,73,65,52,52,91,125,106,56,83,75,82,64,98,275,76,76,60,59,61,59,70,79,99,92,80,102,96,72,78,84,176,191,117,128,55,659,98,64,54", "endOffsets": "24235,24389,24484,24774,25636,25703,25800,25869,26098,26163,26261,28115,28355,28540,28846,28936,29511,29960,30026,30542,31451,31694,31820,31927,31984,32068,32619,32864,33263,33439,33715,33792,33869,34091,34272,34334,34627,35203,35283,35474,35670,37287,39680,39777,39850,39929,40014,41735,42402,42520,42649,42705,43434,43533,43598,45309"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,200,262,330,407,465,524,577,653,730,811,910,1009,1095,1175,1253,1336,1433,1526,1595,1685,1772,1860,1969,2050,2140,2216,2313,2392,2488,2576,2665,2750,2851,2926,2999,3103,3200,3272,3337,4014,4666,4740,4855,4950,5005,5098,5183,5250,5338,5426,5483,5561,5610,5687,5793,5865,5940,6007,6073,6119,6198,6290,6362,6409,6457,6528,6586,6646,6826,6988,7112,7179,7263,7341,7438,7518,7600,7673,7762,7839,7941,8025,8110,8163,8295,8343,8397,8466,8532,8601,8665,8735,8823,8890,8941", "endColumns": "67,76,61,67,76,57,58,52,75,76,80,98,98,85,79,77,82,96,92,68,89,86,87,108,80,89,75,96,78,95,87,88,84,100,74,72,103,96,71,64,676,651,73,114,94,54,92,84,66,87,87,56,77,48,76,105,71,74,66,65,45,78,91,71,46,47,70,57,59,179,161,123,66,83,77,96,79,81,72,88,76,101,83,84,52,131,47,53,68,65,68,63,69,87,66,50,79", "endOffsets": "118,195,257,325,402,460,519,572,648,725,806,905,1004,1090,1170,1248,1331,1428,1521,1590,1680,1767,1855,1964,2045,2135,2211,2308,2387,2483,2571,2660,2745,2846,2921,2994,3098,3195,3267,3332,4009,4661,4735,4850,4945,5000,5093,5178,5245,5333,5421,5478,5556,5605,5682,5788,5860,5935,6002,6068,6114,6193,6285,6357,6404,6452,6523,6581,6641,6821,6983,7107,7174,7258,7336,7433,7513,7595,7668,7757,7834,7936,8020,8105,8158,8290,8338,8392,8461,8527,8596,8660,8730,8818,8885,8936,9016"}, "to": {"startLines": "197,198,199,200,201,202,203,207,208,209,210,213,215,216,218,223,227,242,245,246,247,249,250,251,253,257,258,259,260,261,262,263,264,265,266,268,271,272,278,279,280,291,292,293,294,295,296,297,298,299,300,301,302,309,310,311,314,315,316,317,321,326,327,328,329,330,335,336,337,338,339,340,344,345,354,355,357,358,362,363,365,370,377,378,449,450,451,453,458,471,472,473,474,475,476,477,493", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18298,18366,18443,18505,18573,18650,18708,19006,19059,19135,19212,19436,19613,19712,19865,20214,20501,21518,21762,21855,21924,22080,22167,22255,22424,22727,22817,22893,22990,23069,23165,23253,23342,23427,23528,23677,23926,24030,24779,24851,24916,26507,27159,27233,27348,27443,27498,27591,27676,27743,27831,27919,27976,28545,28594,28671,28941,29013,29088,29155,29657,30031,30110,30202,30274,30321,30611,30682,30740,30800,30980,31142,31456,31523,32373,32451,32624,32704,33041,33114,33268,33874,34403,34487,41827,41880,42012,42161,42710,44784,44850,44919,44983,45053,45141,45208,46579", "endColumns": "67,76,61,67,76,57,58,52,75,76,80,98,98,85,79,77,82,96,92,68,89,86,87,108,80,89,75,96,78,95,87,88,84,100,74,72,103,96,71,64,676,651,73,114,94,54,92,84,66,87,87,56,77,48,76,105,71,74,66,65,45,78,91,71,46,47,70,57,59,179,161,123,66,83,77,96,79,81,72,88,76,101,83,84,52,131,47,53,68,65,68,63,69,87,66,50,79", "endOffsets": "18361,18438,18500,18568,18645,18703,18762,19054,19130,19207,19288,19530,19707,19793,19940,20287,20579,21610,21850,21919,22009,22162,22250,22359,22500,22812,22888,22985,23064,23160,23248,23337,23422,23523,23598,23745,24025,24122,24846,24911,25588,27154,27228,27343,27438,27493,27586,27671,27738,27826,27914,27971,28049,28589,28666,28772,29008,29083,29150,29216,29698,30105,30197,30269,30316,30364,30677,30735,30795,30975,31137,31261,31518,31602,32446,32543,32699,32781,33109,33198,33340,33971,34482,34567,41875,42007,42055,42210,42774,44845,44914,44978,45048,45136,45203,45254,46654"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,190,252,337,399,469,528,604,676,742,802", "endColumns": "77,56,61,84,61,69,58,75,71,65,59,68", "endOffsets": "128,185,247,332,394,464,523,599,671,737,797,866"}, "to": {"startLines": "212,222,224,225,226,230,238,241,244,248,252,256", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19358,20157,20292,20354,20439,20720,21250,21442,21690,22014,22364,22658", "endColumns": "77,56,61,84,61,69,58,75,71,65,59,68", "endOffsets": "19431,20209,20349,20434,20496,20785,21304,21513,21757,22075,22419,22722"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca6706086df4d54b31c3004876d79fd4\\transformed\\jetified-facebook-login-18.0.3\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,217,362,501,612,693,779,860,949,1040,1153,1262,1353,1444,1545,1661,1741,1914,2011,2111,2224,2332", "endColumns": "161,144,138,110,80,85,80,88,90,112,108,90,90,100,115,79,172,96,99,112,107,136", "endOffsets": "212,357,496,607,688,774,855,944,1035,1148,1257,1348,1439,1540,1656,1736,1909,2006,2106,2219,2327,2464"}, "to": {"startLines": "53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4851,5013,5158,5297,5408,5489,5575,5656,5745,5836,5949,6058,6149,6240,6341,6457,6537,6710,6807,6907,7020,7128", "endColumns": "161,144,138,110,80,85,80,88,90,112,108,90,90,100,115,79,172,96,99,112,107,136", "endOffsets": "5008,5153,5292,5403,5484,5570,5651,5740,5831,5944,6053,6144,6235,6336,6452,6532,6705,6802,6902,7015,7123,7260"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,2812"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,191", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "311,414,509,623,709,809,922,999,1074,1165,1258,1352,1446,1546,1639,1734,1832,1923,2014,2092,2195,2293,2389,2493,2592,2693,2846,17813", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "409,504,618,704,804,917,994,1069,1160,1253,1347,1441,1541,1634,1729,1827,1918,2009,2087,2190,2288,2384,2488,2587,2688,2841,2938,17888"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,206", "endColumns": "74,75,72", "endOffsets": "125,201,274"}, "to": {"startLines": "52,99,104", "startColumns": "4,4,4", "startOffsets": "4776,10050,10424", "endColumns": "74,75,72", "endOffsets": "4846,10121,10492"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,266,378", "endColumns": "109,100,111,96", "endOffsets": "160,261,373,470"}, "to": {"startLines": "95,106,107,108", "startColumns": "4,4,4,4", "startOffsets": "9655,10589,10690,10802", "endColumns": "109,100,111,96", "endOffsets": "9760,10685,10797,10894"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,198,265,349,416,477,546,613,677,745,817,877,936,1009,1073,1143,1206,1281,1345,1434,1508,1593,1684,1782,1869,1917,1965,2042,2106,2173,2243,2337,2423,2511", "endColumns": "64,77,66,83,66,60,68,66,63,67,71,59,58,72,63,69,62,74,63,88,73,84,90,97,86,47,47,76,63,66,69,93,85,87,83", "endOffsets": "115,193,260,344,411,472,541,608,672,740,812,872,931,1004,1068,1138,1201,1276,1340,1429,1503,1588,1679,1777,1864,1912,1960,2037,2101,2168,2238,2332,2418,2506,2590"}, "to": {"startLines": "211,214,217,219,220,221,228,229,231,232,233,234,235,236,237,239,240,243,254,255,267,269,270,304,305,319,331,332,334,341,342,351,352,360,361", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19293,19535,19798,19945,20029,20096,20584,20653,20790,20854,20922,20994,21054,21113,21186,21309,21379,21615,22505,22569,23603,23750,23835,28120,28218,29516,30369,30417,30547,31266,31333,32073,32167,32869,32957", "endColumns": "64,77,66,83,66,60,68,66,63,67,71,59,58,72,63,69,62,74,63,88,73,84,90,97,86,47,47,76,63,66,69,93,85,87,83", "endOffsets": "19353,19608,19860,20024,20091,20152,20648,20715,20849,20917,20989,21049,21108,21181,21245,21374,21437,21685,22564,22653,23672,23830,23921,28213,28300,29559,30412,30489,30606,31328,31398,32162,32248,32952,33036"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\97b0e46e5034b62169defac2cb4fe8fb\\transformed\\preference-1.2.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,262,340,482,651,730", "endColumns": "69,86,77,141,168,78,75", "endOffsets": "170,257,335,477,646,725,801"}, "to": {"startLines": "94,103,184,188,510,516,517", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "9585,10337,17225,17509,48163,48774,48853", "endColumns": "69,86,77,141,168,78,75", "endOffsets": "9650,10419,17298,17646,48327,48848,48924"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\af486666b169eb5d50216ab5d4cc9553\\transformed\\biometric-1.1.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,249,371,515,650,786,913,1054,1154,1295,1439", "endColumns": "105,87,121,143,134,135,126,140,99,140,143,126", "endOffsets": "156,244,366,510,645,781,908,1049,1149,1290,1434,1561"}, "to": {"startLines": "93,97,109,110,111,112,113,114,115,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9479,9862,10899,11021,11165,11300,11436,11563,11704,11804,11945,12089", "endColumns": "105,87,121,143,134,135,126,140,99,140,143,126", "endOffsets": "9580,9945,11016,11160,11295,11431,11558,11699,11799,11940,12084,12211"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-nb\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,70", "endOffsets": "258,329"}, "to": {"startLines": "119,518", "startColumns": "4,4", "startOffsets": "12216,48929", "endColumns": "60,74", "endOffsets": "12272,48999"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,338,411,498,586,666,765,884,966,1025,1089,1181,1249,1309,1396,1460,1522,1586,1654,1719,1773,1882,1940,2002,2056,2131,2251,2333,2410,2500,2584,2664,2798,2876,2956,3079,3167,3245,3299,3350,3416,3484,3558,3629,3705,3776,3854,3924,3994,4094,4183,4261,4349,4439,4511,4583,4667,4718,4796,4862,4943,5026,5088,5152,5215,5284,5384,5488,5581,5681,5739,5794,5872,5956,6034", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,76,72,86,87,79,98,118,81,58,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,76,89,83,79,133,77,79,122,87,77,53,50,65,67,73,70,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77,83,77,71", "endOffsets": "256,333,406,493,581,661,760,879,961,1020,1084,1176,1244,1304,1391,1455,1517,1581,1649,1714,1768,1877,1935,1997,2051,2126,2246,2328,2405,2495,2579,2659,2793,2871,2951,3074,3162,3240,3294,3345,3411,3479,3553,3624,3700,3771,3849,3919,3989,4089,4178,4256,4344,4434,4506,4578,4662,4713,4791,4857,4938,5021,5083,5147,5210,5279,5379,5483,5576,5676,5734,5789,5867,5951,6029,6101"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,101,102,105,120,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,187,192,193,195", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3171,3248,3321,3408,3496,4302,4401,4520,10214,10273,10497,12277,12509,12569,12656,12720,12782,12846,12914,12979,13033,13142,13200,13262,13316,13391,13511,13593,13670,13760,13844,13924,14058,14136,14216,14339,14427,14505,14559,14610,14676,14744,14818,14889,14965,15036,15114,15184,15254,15354,15443,15521,15609,15699,15771,15843,15927,15978,16056,16122,16203,16286,16348,16412,16475,16544,16644,16748,16841,16941,16999,17431,17893,17977,18125", "endLines": "5,35,36,37,38,39,47,48,49,101,102,105,120,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,187,192,193,195", "endColumns": "12,76,72,86,87,79,98,118,81,58,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,76,89,83,79,133,77,79,122,87,77,53,50,65,67,73,70,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77,83,77,71", "endOffsets": "306,3243,3316,3403,3491,3571,4396,4515,4597,10268,10332,10584,12340,12564,12651,12715,12777,12841,12909,12974,13028,13137,13195,13257,13311,13386,13506,13588,13665,13755,13839,13919,14053,14131,14211,14334,14422,14500,14554,14605,14671,14739,14813,14884,14960,15031,15109,15179,15249,15349,15438,15516,15604,15694,15766,15838,15922,15973,16051,16117,16198,16281,16343,16407,16470,16539,16639,16743,16836,16936,16994,17049,17504,17972,18050,18192"}}]}]}