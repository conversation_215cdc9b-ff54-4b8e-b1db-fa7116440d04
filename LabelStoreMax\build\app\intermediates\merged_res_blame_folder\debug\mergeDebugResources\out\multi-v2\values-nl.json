{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-113:/values-nl/values-nl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-nl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,444,568,675,838,961,1080,1182,1356,1458,1623,1745,1904,2082,2146,2205", "endColumns": "103,146,123,106,162,122,118,101,173,101,164,121,158,177,63,58,74", "endOffsets": "296,443,567,674,837,960,1079,1181,1355,1457,1622,1744,1903,2081,2145,2204,2279"}, "to": {"startLines": "76,77,78,79,80,81,82,83,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7551,7659,7810,7938,8049,8216,8343,8466,8715,8893,8999,9168,9294,9457,9639,9707,9770", "endColumns": "107,150,127,110,166,126,122,105,177,105,168,125,162,181,67,62,78", "endOffsets": "7654,7805,7933,8044,8211,8338,8461,8567,8888,8994,9163,9289,9452,9634,9702,9765,9844"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\97b0e46e5034b62169defac2cb4fe8fb\\transformed\\preference-1.2.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,267,348,494,663,743", "endColumns": "71,89,80,145,168,79,76", "endOffsets": "172,262,343,489,658,738,815"}, "to": {"startLines": "95,104,185,189,519,525,526", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "9963,10714,17793,18085,50761,51376,51456", "endColumns": "71,89,80,145,168,79,76", "endOffsets": "10030,10799,17869,18226,50925,51451,51528"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,264,333,411,469,533,588,663,743,824,921,1018,1103,1182,1264,1352,1450,1541,1608,1696,1785,1871,1978,2060,2148,2222,2317,2390,2481,2569,2653,2735,2835,2904,2979,3089,3191,3263,3328,4090,4828,4905,5022,5123,5178,5281,5379,5444,5533,5622,5679,5760,5812,5892,6006,6077,6150,6217,6283,6332,6415,6512,6596,6643,6692,6762,6820,6886,7078,7245,7375,7440,7522,7607,7707,7791,7884,7959,8045,8119,8222,8309,8404,8457,8597,8651,8708,8783,8855,8930,8997,9067,9160,9235,9287", "endColumns": "68,77,61,68,77,57,63,54,74,79,80,96,96,84,78,81,87,97,90,66,87,88,85,106,81,87,73,94,72,90,87,83,81,99,68,74,109,101,71,64,761,737,76,116,100,54,102,97,64,88,88,56,80,51,79,113,70,72,66,65,48,82,96,83,46,48,69,57,65,191,166,129,64,81,84,99,83,92,74,85,73,102,86,94,52,139,53,56,74,71,74,66,69,92,74,51,77", "endOffsets": "119,197,259,328,406,464,528,583,658,738,819,916,1013,1098,1177,1259,1347,1445,1536,1603,1691,1780,1866,1973,2055,2143,2217,2312,2385,2476,2564,2648,2730,2830,2899,2974,3084,3186,3258,3323,4085,4823,4900,5017,5118,5173,5276,5374,5439,5528,5617,5674,5755,5807,5887,6001,6072,6145,6212,6278,6327,6410,6507,6591,6638,6687,6757,6815,6881,7073,7240,7370,7435,7517,7602,7702,7786,7879,7954,8040,8114,8217,8304,8399,8452,8592,8646,8703,8778,8850,8925,8992,9062,9155,9230,9282,9360"}, "to": {"startLines": "205,206,207,208,209,210,211,215,216,217,218,221,223,224,226,231,235,250,253,254,255,257,258,259,261,265,266,267,268,269,270,271,272,273,274,276,279,280,286,287,288,299,300,301,302,303,304,305,306,307,308,309,310,317,318,319,322,323,324,325,329,334,335,336,337,338,343,344,345,346,347,348,352,353,363,364,366,367,371,372,374,379,386,387,458,459,460,462,467,480,481,482,483,484,485,486,502", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19433,19502,19580,19642,19711,19789,19847,20161,20216,20291,20371,20591,20764,20861,21014,21370,21662,22696,22940,23031,23098,23254,23343,23429,23597,23897,23985,24059,24154,24227,24318,24406,24490,24572,24672,24818,25059,25169,25964,26036,26101,27815,28553,28630,28747,28848,28903,29006,29104,29169,29258,29347,29404,29995,30047,30127,30415,30486,30559,30626,31170,31547,31630,31727,31811,31858,32157,32227,32285,32351,32543,32710,33028,33093,34063,34148,34327,34411,34779,34854,35007,35658,36208,36295,43991,44044,44184,44346,44948,47223,47295,47370,47437,47507,47600,47675,49084", "endColumns": "68,77,61,68,77,57,63,54,74,79,80,96,96,84,78,81,87,97,90,66,87,88,85,106,81,87,73,94,72,90,87,83,81,99,68,74,109,101,71,64,761,737,76,116,100,54,102,97,64,88,88,56,80,51,79,113,70,72,66,65,48,82,96,83,46,48,69,57,65,191,166,129,64,81,84,99,83,92,74,85,73,102,86,94,52,139,53,56,74,71,74,66,69,92,74,51,77", "endOffsets": "19497,19575,19637,19706,19784,19842,19906,20211,20286,20366,20447,20683,20856,20941,21088,21447,21745,22789,23026,23093,23181,23338,23424,23531,23674,23980,24054,24149,24222,24313,24401,24485,24567,24667,24736,24888,25164,25266,26031,26096,26858,28548,28625,28742,28843,28898,29001,29099,29164,29253,29342,29399,29480,30042,30122,30236,30481,30554,30621,30687,31214,31625,31722,31806,31853,31902,32222,32280,32346,32538,32705,32835,33088,33170,34143,34243,34406,34499,34849,34935,35076,35756,36290,36385,44039,44179,44233,44398,45018,47290,47365,47432,47502,47595,47670,47722,49157"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,2827", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,619,739,817,893,985,1079,1174,1268,1368,1462,1558,1653,1745,1837,1919,2030,2133,2232,2347,2461,2564,2719,2822,2905"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,437,542,649,734,838,958,1036,1112,1204,1298,1393,1487,1587,1681,1777,1872,1964,2056,2138,2249,2352,2451,2566,2680,2783,2938,18395", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "432,537,644,729,833,953,1031,1107,1199,1293,1388,1482,1582,1676,1772,1867,1959,2051,2133,2244,2347,2446,2561,2675,2778,2933,3036,18473"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,280,377,476,561,637,733,820,909,974,1039,1120,1203,1280,1364,1434", "endColumns": "91,82,96,98,84,75,95,86,88,64,64,80,82,76,83,69,119", "endOffsets": "192,275,372,471,556,632,728,815,904,969,1034,1115,1198,1275,1359,1429,1549"}, "to": {"startLines": "50,51,97,99,101,122,123,183,184,186,187,190,191,195,520,521,522", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4748,4840,10138,10326,10505,12725,12801,17617,17704,17874,17939,18231,18312,18637,50930,51014,51084", "endColumns": "91,82,96,98,84,75,95,86,88,64,64,80,82,76,83,69,119", "endOffsets": "4835,4918,10230,10420,10585,12796,12892,17699,17788,17934,17999,18307,18390,18709,51009,51079,51199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,137,217", "endColumns": "81,79,78", "endOffsets": "132,212,291"}, "to": {"startLines": "52,100,105", "startColumns": "4,4,4", "startOffsets": "4923,10425,10804", "endColumns": "81,79,78", "endOffsets": "5000,10500,10878"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-nl\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,71", "endOffsets": "258,330"}, "to": {"startLines": "120,527", "startColumns": "4,4", "startOffsets": "12585,51533", "endColumns": "60,75", "endOffsets": "12641,51604"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,223,305,395,482,563,643,780,870,971,1061,1190,1251,1379,1445,1645,1864,2020,2112,2213,2306,2400,2581,2678,2777,3030,3142,3280,3488,3714,3806,3935,4157,4252,4314,4381,4464,4560,4661,4741,4835,4922,5024,5232,5302,5378,5454,5590,5666,5756,5822,5911,5983,6053,6169,6256,6365,6464,6548,6639,6712,6802,6863,6970,7078,7187,7321,7386,7481,7581,7717,7789,7879,7987,8047,8109,8188,8295,8468,8777,9107,9205,9282,9378,9452,9563,9681,9769,9851,9924,10012,10095,10187,10308,10439,10506,10584,10644,10837,10906,10966,11047,11119,11197,11284,11430,11562,11685,11779,11860,11949,12025", "endColumns": "74,92,81,89,86,80,79,136,89,100,89,128,60,127,65,199,218,155,91,100,92,93,180,96,98,252,111,137,207,225,91,128,221,94,61,66,82,95,100,79,93,86,101,207,69,75,75,135,75,89,65,88,71,69,115,86,108,98,83,90,72,89,60,106,107,108,133,64,94,99,135,71,89,107,59,61,78,106,172,308,329,97,76,95,73,110,117,87,81,72,87,82,91,120,130,66,77,59,192,68,59,80,71,77,86,145,131,122,93,80,88,75,157", "endOffsets": "125,218,300,390,477,558,638,775,865,966,1056,1185,1246,1374,1440,1640,1859,2015,2107,2208,2301,2395,2576,2673,2772,3025,3137,3275,3483,3709,3801,3930,4152,4247,4309,4376,4459,4555,4656,4736,4830,4917,5019,5227,5297,5373,5449,5585,5661,5751,5817,5906,5978,6048,6164,6251,6360,6459,6543,6634,6707,6797,6858,6965,7073,7182,7316,7381,7476,7576,7712,7784,7874,7982,8042,8104,8183,8290,8463,8772,9102,9200,9277,9373,9447,9558,9676,9764,9846,9919,10007,10090,10182,10303,10434,10501,10579,10639,10832,10901,10961,11042,11114,11192,11279,11425,11557,11680,11774,11855,11944,12020,12178"}, "to": {"startLines": "212,213,214,284,296,297,298,315,328,330,331,362,380,382,385,389,390,391,394,396,398,399,400,401,402,403,404,405,406,407,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,457,461,471,472,473,474,475,476,477,478,479,488,489,490,491,492,493,494,495,496,497,498,499,500,501,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19911,19986,20079,25639,27567,27654,27735,29794,31080,31219,31320,33934,35761,35890,36142,36450,36650,36869,37188,37378,37582,37675,37769,37950,38047,38146,38399,38511,38649,38857,39083,39261,39390,39612,39707,39769,39836,39919,40015,40116,40196,40290,40377,40479,40687,40757,40833,40909,41045,41121,41211,41277,41366,41438,41508,42062,42149,42258,42357,42441,42532,42605,42695,42756,42863,42971,43080,43214,43279,43374,43474,43610,43901,44238,45928,45988,46050,46129,46236,46409,46718,47048,47146,47782,47878,47952,48063,48181,48269,48351,48424,48512,48595,48687,48808,48939,49006,49162,49222,49415,49484,49544,49625,49697,49775,49862,50008,50140,50263,50357,50438,50527,50603", "endColumns": "74,92,81,89,86,80,79,136,89,100,89,128,60,127,65,199,218,155,91,100,92,93,180,96,98,252,111,137,207,225,91,128,221,94,61,66,82,95,100,79,93,86,101,207,69,75,75,135,75,89,65,88,71,69,115,86,108,98,83,90,72,89,60,106,107,108,133,64,94,99,135,71,89,107,59,61,78,106,172,308,329,97,76,95,73,110,117,87,81,72,87,82,91,120,130,66,77,59,192,68,59,80,71,77,86,145,131,122,93,80,88,75,157", "endOffsets": "19981,20074,20156,25724,27649,27730,27810,29926,31165,31315,31405,34058,35817,36013,36203,36645,36864,37020,37275,37474,37670,37764,37945,38042,38141,38394,38506,38644,38852,39078,39170,39385,39607,39702,39764,39831,39914,40010,40111,40191,40285,40372,40474,40682,40752,40828,40904,41040,41116,41206,41272,41361,41433,41503,41619,42144,42253,42352,42436,42527,42600,42690,42751,42858,42966,43075,43209,43274,43369,43469,43605,43677,43986,44341,45983,46045,46124,46231,46404,46713,47043,47141,47218,47873,47947,48058,48176,48264,48346,48419,48507,48590,48682,48803,48934,49001,49079,49217,49410,49479,49539,49620,49692,49770,49857,50003,50135,50258,50352,50433,50522,50598,50756"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca6706086df4d54b31c3004876d79fd4\\transformed\\jetified-facebook-login-18.0.3\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,212,347,495,606,695,788,861,953,1045,1158,1268,1360,1452,1553,1669,1754,1836,2031,2125,2233,2346,2457", "endColumns": "156,134,147,110,88,92,72,91,91,112,109,91,91,100,115,84,81,194,93,107,112,110,143", "endOffsets": "207,342,490,601,690,783,856,948,1040,1153,1263,1355,1447,1548,1664,1749,1831,2026,2120,2228,2341,2452,2596"}, "to": {"startLines": "53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5005,5162,5297,5445,5556,5645,5738,5811,5903,5995,6108,6218,6310,6402,6503,6619,6704,6786,6981,7075,7183,7296,7407", "endColumns": "156,134,147,110,88,92,72,91,91,112,109,91,91,100,115,84,81,194,93,107,112,110,143", "endOffsets": "5157,5292,5440,5551,5640,5733,5806,5898,5990,6103,6213,6305,6397,6498,6614,6699,6781,6976,7070,7178,7291,7402,7546"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "354", "startColumns": "4", "startOffsets": "33175", "endColumns": "87", "endOffsets": "33258"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\587767e501a9ab66a3f91617d285250f\\transformed\\core-1.16.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "40,41,42,43,44,45,46,197", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3714,3816,3918,4018,4118,4225,4329,18786", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "3811,3913,4013,4113,4220,4324,4443,18882"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,355,437,514,612,706,803,925,1006,1066,1130,1219,1298,1361,1454,1516,1582,1640,1713,1777,1833,1955,2012,2074,2130,2206,2340,2425,2504,2602,2688,2774,2912,2993,3072,3196,3286,3363,3420,3471,3537,3615,3698,3769,3845,3920,3999,4072,4143,4252,4346,4424,4513,4603,4677,4758,4845,4898,4977,5044,5125,5209,5271,5335,5398,5469,5577,5689,5791,5902,5963,6018,6099,6182,6258", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,85,81,76,97,93,96,121,80,59,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,78,97,85,85,137,80,78,123,89,76,56,50,65,77,82,70,75,74,78,72,70,108,93,77,88,89,73,80,86,52,78,66,80,83,61,63,62,70,107,111,101,110,60,54,80,82,75,71", "endOffsets": "264,350,432,509,607,701,798,920,1001,1061,1125,1214,1293,1356,1449,1511,1577,1635,1708,1772,1828,1950,2007,2069,2125,2201,2335,2420,2499,2597,2683,2769,2907,2988,3067,3191,3281,3358,3415,3466,3532,3610,3693,3764,3840,3915,3994,4067,4138,4247,4341,4419,4508,4598,4672,4753,4840,4893,4972,5039,5120,5204,5266,5330,5393,5464,5572,5684,5786,5897,5958,6013,6094,6177,6253,6325"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3277,3363,3445,3522,3620,4448,4545,4667,10590,10650,10883,12646,12897,12960,13053,13115,13181,13239,13312,13376,13432,13554,13611,13673,13729,13805,13939,14024,14103,14201,14287,14373,14511,14592,14671,14795,14885,14962,15019,15070,15136,15214,15297,15368,15444,15519,15598,15671,15742,15851,15945,16023,16112,16202,16276,16357,16444,16497,16576,16643,16724,16808,16870,16934,16997,17068,17176,17288,17390,17501,17562,18004,18478,18561,18714", "endLines": "5,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "endColumns": "12,85,81,76,97,93,96,121,80,59,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,78,97,85,85,137,80,78,123,89,76,56,50,65,77,82,70,75,74,78,72,70,108,93,77,88,89,73,80,86,52,78,66,80,83,61,63,62,70,107,111,101,110,60,54,80,82,75,71", "endOffsets": "314,3358,3440,3517,3615,3709,4540,4662,4743,10645,10709,10967,12720,12955,13048,13110,13176,13234,13307,13371,13427,13549,13606,13668,13724,13800,13934,14019,14098,14196,14282,14368,14506,14587,14666,14790,14880,14957,15014,15065,15131,15209,15292,15363,15439,15514,15593,15666,15737,15846,15940,16018,16107,16197,16271,16352,16439,16492,16571,16638,16719,16803,16865,16929,16992,17063,17171,17283,17385,17496,17557,17612,18080,18556,18632,18781"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\af486666b169eb5d50216ab5d4cc9553\\transformed\\biometric-1.1.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,260,381,519,653,782,910,1052,1150,1290,1436", "endColumns": "113,90,120,137,133,128,127,141,97,139,145,125", "endOffsets": "164,255,376,514,648,777,905,1047,1145,1285,1431,1557"}, "to": {"startLines": "94,98,110,111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9849,10235,11283,11404,11542,11676,11805,11933,12075,12173,12313,12459", "endColumns": "113,90,120,137,133,128,127,141,97,139,145,125", "endOffsets": "9958,10321,11399,11537,11671,11800,11928,12070,12168,12308,12454,12580"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,194,262,346,417,478,550,617,680,748,818,883,946,1020,1084,1151,1214,1290,1355,1441,1518,1598,1684,1791,1874,1925,1978,2058,2122,2187,2257,2358,2443,2540", "endColumns": "62,75,67,83,70,60,71,66,62,67,69,64,62,73,63,66,62,75,64,85,76,79,85,106,82,50,52,79,63,64,69,100,84,96,95", "endOffsets": "113,189,257,341,412,473,545,612,675,743,813,878,941,1015,1079,1146,1209,1285,1350,1436,1513,1593,1679,1786,1869,1920,1973,2053,2117,2182,2252,2353,2438,2535,2631"}, "to": {"startLines": "219,222,225,227,228,229,236,237,239,240,241,242,243,244,245,247,248,251,262,263,275,277,278,312,313,327,339,340,342,349,350,360,361,369,370", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20452,20688,20946,21093,21177,21248,21750,21822,21963,22026,22094,22164,22229,22292,22366,22489,22556,22794,23679,23744,24741,24893,24973,29549,29656,31029,31907,31960,32093,32840,32905,33748,33849,34586,34683", "endColumns": "62,75,67,83,70,60,71,66,62,67,69,64,62,73,63,66,62,75,64,85,76,79,85,106,82,50,52,79,63,64,69,100,84,96,95", "endOffsets": "20510,20759,21009,21172,21243,21304,21817,21884,22021,22089,22159,22224,22287,22361,22425,22551,22614,22865,23739,23825,24813,24968,25054,29651,29734,31075,31955,32035,32152,32900,32970,33844,33929,34678,34774"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,85", "endOffsets": "136,222"}, "to": {"startLines": "523,524", "startColumns": "4,4", "startOffsets": "51204,51290", "endColumns": "85,85", "endOffsets": "51285,51371"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,144,246,313,380,447,524", "endColumns": "88,101,66,66,66,76,76", "endOffsets": "139,241,308,375,442,519,596"}, "to": {"startLines": "198,199,200,201,202,203,204", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "18887,18976,19078,19145,19212,19279,19356", "endColumns": "88,101,66,66,66,76,76", "endOffsets": "18971,19073,19140,19207,19274,19351,19428"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-nl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "84", "startColumns": "4", "startOffsets": "8572", "endColumns": "142", "endOffsets": "8710"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,370", "endColumns": "102,100,110,98", "endOffsets": "153,254,365,464"}, "to": {"startLines": "96,107,108,109", "startColumns": "4,4,4,4", "startOffsets": "10035,10972,11073,11184", "endColumns": "102,100,110,98", "endOffsets": "10133,11068,11179,11278"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,325,423,658,704,774,873,945,1213,1273,1362,1426,1481,1545,1624,1719,2056,2127,2193,2246,2299,2391,2524,2643,2700,2784,2863,2945,3012,3108,3434,3511,3589,3657,3717,3781,3841,3914,4004,4102,4205,4291,4389,4489,4563,4642,4729,4948,5180,5298,5428,5493,6232,6334,6398", "endColumns": "109,159,97,234,45,69,98,71,267,59,88,63,54,63,78,94,336,70,65,52,52,91,132,118,56,83,78,81,66,95,325,76,77,67,59,63,59,72,89,97,102,85,97,99,73,78,86,218,231,117,129,64,738,101,63,54", "endOffsets": "160,320,418,653,699,769,868,940,1208,1268,1357,1421,1476,1540,1619,1714,2051,2122,2188,2241,2294,2386,2519,2638,2695,2779,2858,2940,3007,3103,3429,3506,3584,3652,3712,3776,3836,3909,3999,4097,4200,4286,4384,4484,4558,4637,4724,4943,5175,5293,5423,5488,6227,6329,6393,6448"}, "to": {"startLines": "281,282,283,285,289,290,291,292,293,294,295,311,314,316,320,321,326,332,333,341,351,355,356,357,358,359,365,368,373,375,376,377,378,381,383,384,388,392,393,395,397,409,434,435,436,437,438,456,463,464,465,466,468,469,470,487", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "25271,25381,25541,25729,26863,26909,26979,27078,27150,27418,27478,29485,29739,29931,30241,30320,30692,31410,31481,32040,32975,33263,33355,33488,33607,33664,34248,34504,34940,35081,35177,35503,35580,35822,36018,36078,36390,37025,37098,37280,37479,39175,41624,41722,41822,41896,41975,43682,44403,44635,44753,44883,45023,45762,45864,47727", "endColumns": "109,159,97,234,45,69,98,71,267,59,88,63,54,63,78,94,336,70,65,52,52,91,132,118,56,83,78,81,66,95,325,76,77,67,59,63,59,72,89,97,102,85,97,99,73,78,86,218,231,117,129,64,738,101,63,54", "endOffsets": "25376,25536,25634,25959,26904,26974,27073,27145,27413,27473,27562,29544,29789,29990,30315,30410,31024,31476,31542,32088,33023,33350,33483,33602,33659,33743,34322,34581,35002,35172,35498,35575,35653,35885,36073,36137,36445,37093,37183,37373,37577,39256,41717,41817,41891,41970,42057,43896,44630,44748,44878,44943,45757,45859,45923,47777"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e3c1b17a9ffaafd0471e6527f4794f6b\\transformed\\jetified-credentials-1.5.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,121", "endOffsets": "164,286"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3041,3155", "endColumns": "113,121", "endOffsets": "3150,3272"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,192,254,335,402,476,535,612,682,750,811", "endColumns": "75,60,61,80,66,73,58,76,69,67,60,66", "endOffsets": "126,187,249,330,397,471,530,607,677,745,806,873"}, "to": {"startLines": "220,230,232,233,234,238,246,249,252,256,260,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20515,21309,21452,21514,21595,21889,22430,22619,22870,23186,23536,23830", "endColumns": "75,60,61,80,66,73,58,76,69,67,60,66", "endOffsets": "20586,21365,21509,21590,21657,21958,22484,22691,22935,23249,23592,23892"}}]}]}