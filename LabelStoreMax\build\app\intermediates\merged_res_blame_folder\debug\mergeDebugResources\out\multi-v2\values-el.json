{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-113:/values-el/values-el.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\97b0e46e5034b62169defac2cb4fe8fb\\transformed\\preference-1.2.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,265,348,490,659,744", "endColumns": "72,86,82,141,168,84,79", "endOffsets": "173,260,343,485,654,739,819"}, "to": {"startLines": "95,104,185,189,199,205,206", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "10212,10996,18312,18624,19524,20165,20250", "endColumns": "72,86,82,141,168,84,79", "endOffsets": "10280,11078,18390,18761,19688,20245,20325"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e3c1b17a9ffaafd0471e6527f4794f6b\\transformed\\jetified-credentials-1.5.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,176", "endColumns": "120,122", "endOffsets": "171,294"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3101,3222", "endColumns": "120,122", "endOffsets": "3217,3340"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\587767e501a9ab66a3f91617d285250f\\transformed\\core-1.16.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "40,41,42,43,44,45,46,197", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3787,3885,3988,4088,4191,4299,4405,19334", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "3880,3983,4083,4186,4294,4400,4517,19430"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,290,391,496,588,669,763,852,942,1012,1080,1161,1243,1318,1397,1467", "endColumns": "98,85,100,104,91,80,93,88,89,69,67,80,81,74,78,69,122", "endOffsets": "199,285,386,491,583,664,758,847,937,1007,1075,1156,1238,1313,1392,1462,1585"}, "to": {"startLines": "50,51,97,99,101,122,123,183,184,186,187,190,191,195,200,201,202", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4827,4926,10395,10587,10775,13129,13210,18133,18222,18395,18465,18766,18847,19178,19693,19772,19842", "endColumns": "98,85,100,104,91,80,93,88,89,69,67,80,81,74,78,69,122", "endOffsets": "4921,5007,10491,10687,10862,13205,13299,18217,18307,18460,18528,18842,18924,19248,19767,19837,19960"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-el\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,72", "endOffsets": "258,331"}, "to": {"startLines": "120,207", "startColumns": "4,4", "startOffsets": "12986,20330", "endColumns": "60,76", "endOffsets": "13042,20402"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca6706086df4d54b31c3004876d79fd4\\transformed\\jetified-facebook-login-18.0.3\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,238,388,540,653,740,832,909,999,1089,1204,1316,1410,1504,1609,1730,1814,1902,2105,2198,2305,2417,2527", "endColumns": "182,149,151,112,86,91,76,89,89,114,111,93,93,104,120,83,87,202,92,106,111,109,164", "endOffsets": "233,383,535,648,735,827,904,994,1084,1199,1311,1405,1499,1604,1725,1809,1897,2100,2193,2300,2412,2522,2687"}, "to": {"startLines": "53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5092,5275,5425,5577,5690,5777,5869,5946,6036,6126,6241,6353,6447,6541,6646,6767,6851,6939,7142,7235,7342,7454,7564", "endColumns": "182,149,151,112,86,91,76,89,89,114,111,93,93,104,120,83,87,202,92,106,111,109,164", "endOffsets": "5270,5420,5572,5685,5772,5864,5941,6031,6121,6236,6348,6442,6536,6641,6762,6846,6934,7137,7230,7337,7449,7559,7724"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "198", "startColumns": "4", "startOffsets": "19435", "endColumns": "88", "endOffsets": "19519"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,218", "endColumns": "79,82,78", "endOffsets": "130,213,292"}, "to": {"startLines": "52,100,105", "startColumns": "4,4,4", "startOffsets": "5012,10692,11083", "endColumns": "79,82,78", "endOffsets": "5087,10770,11157"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-el\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,482,610,717,894,1015,1129,1230,1415,1519,1685,1810,1984,2125,2190,2248", "endColumns": "106,181,127,106,176,120,113,100,184,103,165,124,173,140,64,57,78", "endOffsets": "299,481,609,716,893,1014,1128,1229,1414,1518,1684,1809,1983,2124,2189,2247,2326"}, "to": {"startLines": "76,77,78,79,80,81,82,83,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7729,7840,8026,8158,8269,8450,8575,8693,8962,9151,9259,9429,9558,9736,9881,9950,10012", "endColumns": "110,185,131,110,180,124,117,104,188,107,169,128,177,144,68,61,82", "endOffsets": "7835,8021,8153,8264,8445,8570,8688,8793,9146,9254,9424,9553,9731,9876,9945,10007,10090"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\af486666b169eb5d50216ab5d4cc9553\\transformed\\biometric-1.1.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,263,383,535,688,828,966,1119,1222,1365,1512", "endColumns": "116,90,119,151,152,139,137,152,102,142,146,132", "endOffsets": "167,258,378,530,683,823,961,1114,1217,1360,1507,1640"}, "to": {"startLines": "94,98,110,111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10095,10496,11604,11724,11876,12029,12169,12307,12460,12563,12706,12853", "endColumns": "116,90,119,151,152,139,137,152,102,142,146,132", "endOffsets": "10207,10582,11719,11871,12024,12164,12302,12455,12558,12701,12848,12981"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,270,358,444,529,625,712,814,931,1017,1080,1146,1246,1328,1391,1482,1545,1610,1672,1741,1803,1857,1995,2052,2113,2167,2240,2393,2478,2557,2653,2737,2821,2960,3041,3126,3267,3357,3443,3498,3549,3615,3693,3778,3849,3932,4004,4084,4164,4235,4342,4434,4506,4603,4700,4774,4848,4950,5006,5093,5165,5253,5345,5407,5471,5534,5604,5720,5829,5938,6043,6102,6157,6248,6336,6411", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,87,85,84,95,86,101,116,85,62,65,99,81,62,90,62,64,61,68,61,53,137,56,60,53,72,152,84,78,95,83,83,138,80,84,140,89,85,54,50,65,77,84,70,82,71,79,79,70,106,91,71,96,96,73,73,101,55,86,71,87,91,61,63,62,69,115,108,108,104,58,54,90,87,74,80", "endOffsets": "265,353,439,524,620,707,809,926,1012,1075,1141,1241,1323,1386,1477,1540,1605,1667,1736,1798,1852,1990,2047,2108,2162,2235,2388,2473,2552,2648,2732,2816,2955,3036,3121,3262,3352,3438,3493,3544,3610,3688,3773,3844,3927,3999,4079,4159,4230,4337,4429,4501,4598,4695,4769,4843,4945,5001,5088,5160,5248,5340,5402,5466,5529,5599,5715,5824,5933,6038,6097,6152,6243,6331,6406,6487"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3345,3433,3519,3604,3700,4522,4624,4741,10867,10930,11162,13047,13304,13367,13458,13521,13586,13648,13717,13779,13833,13971,14028,14089,14143,14216,14369,14454,14533,14629,14713,14797,14936,15017,15102,15243,15333,15419,15474,15525,15591,15669,15754,15825,15908,15980,16060,16140,16211,16318,16410,16482,16579,16676,16750,16824,16926,16982,17069,17141,17229,17321,17383,17447,17510,17580,17696,17805,17914,18019,18078,18533,19015,19103,19253", "endLines": "5,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "endColumns": "12,87,85,84,95,86,101,116,85,62,65,99,81,62,90,62,64,61,68,61,53,137,56,60,53,72,152,84,78,95,83,83,138,80,84,140,89,85,54,50,65,77,84,70,82,71,79,79,70,106,91,71,96,96,73,73,101,55,86,71,87,91,61,63,62,69,115,108,108,104,58,54,90,87,74,80", "endOffsets": "315,3428,3514,3599,3695,3782,4619,4736,4822,10925,10991,11257,13124,13362,13453,13516,13581,13643,13712,13774,13828,13966,14023,14084,14138,14211,14364,14449,14528,14624,14708,14792,14931,15012,15097,15238,15328,15414,15469,15520,15586,15664,15749,15820,15903,15975,16055,16135,16206,16313,16405,16477,16574,16671,16745,16819,16921,16977,17064,17136,17224,17316,17378,17442,17505,17575,17691,17800,17909,18014,18073,18128,18619,19098,19173,19329"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,153", "endColumns": "97,101", "endOffsets": "148,250"}, "to": {"startLines": "203,204", "startColumns": "4,4", "startOffsets": "19965,20063", "endColumns": "97,101", "endOffsets": "20058,20160"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,272,397", "endColumns": "109,106,124,109", "endOffsets": "160,267,392,502"}, "to": {"startLines": "96,107,108,109", "startColumns": "4,4,4,4", "startOffsets": "10285,11262,11369,11494", "endColumns": "109,106,124,109", "endOffsets": "10390,11364,11489,11599"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,939,1030,1123,1218,1312,1412,1505,1600,1697,1788,1879,1964,2075,2184,2286,2397,2507,2615,2786,2886", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "218,329,446,531,637,760,849,934,1025,1118,1213,1307,1407,1500,1595,1692,1783,1874,1959,2070,2179,2281,2392,2502,2610,2781,2881,2967"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "320,438,549,666,751,857,980,1069,1154,1245,1338,1433,1527,1627,1720,1815,1912,2003,2094,2179,2290,2399,2501,2612,2722,2830,3001,18929", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "433,544,661,746,852,975,1064,1149,1240,1333,1428,1522,1622,1715,1810,1907,1998,2089,2174,2285,2394,2496,2607,2717,2825,2996,3096,19010"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-el\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "84", "startColumns": "4", "startOffsets": "8798", "endColumns": "163", "endOffsets": "8957"}}]}]}