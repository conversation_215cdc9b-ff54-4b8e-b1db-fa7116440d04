Flutter crash report.
Please report a bug at https://github.com/flutter/flutter/issues.

## command

flutter run --debug

## exception

PathAccessException: PathAccessException: Deletion failed, path = 'C:\Users\<USER>\AppData\Local\Temp\flutter_tools.73d8ddba\flutter_tool.9fef198a' (OS Error: The process cannot access the file because it is being used by another process.
, errno = 32)

```
#0      _Directory._deleteSync (dart:io/directory_impl.dart:226:7)
#1      FileSystemEntity.deleteSync (dart:io/file_system_entity.dart:425:7)
#2      ForwardingFileSystemEntity.deleteSync (package:file/src/forwarding/forwarding_file_system_entity.dart:70:16)
#3      ErrorHandlingDirectory.deleteSync.<anonymous closure> (package:flutter_tools/src/base/error_handling_io.dart:448:22)
#4      _runSync (package:flutter_tools/src/base/error_handling_io.dart:549:14)
#5      ErrorHandlingDirectory.deleteSync (package:flutter_tools/src/base/error_handling_io.dart:447:12)
#6      ResidentRunner.preExit (package:flutter_tools/src/resident_runner.dart:1393:25)
#7      HotRunner.preExit (package:flutter_tools/src/run_hot.dart:1247:17)
<asynchronous suspension>
#8      ResidentRunner.exit (package:flutter_tools/src/resident_runner.dart:1214:5)
<asynchronous suspension>
#9      TerminalHandler._commonTerminalInputHandler (package:flutter_tools/src/resident_runner.dart:1704:9)
<asynchronous suspension>
#10     TerminalHandler.processTerminalInput (package:flutter_tools/src/resident_runner.dart:1765:7)
<asynchronous suspension>
```

## flutter doctor

```
[32m[✓][39m Flutter (Channel stable, 3.32.6, on Microsoft Windows [Version 10.0.19045.5965], locale en-US) [1,655ms]
    [32m•[39m Flutter version 3.32.6 on channel stable at C:\flutter
    [32m•[39m Upstream repository https://github.com/flutter/flutter.git
    [32m•[39m Framework revision 077b4a4ce1 (2 weeks ago), 2025-07-08 13:31:08 -0700
    [32m•[39m Engine revision 72f2b18bb0
    [32m•[39m Dart version 3.8.1
    [32m•[39m DevTools version 2.45.1

[32m[✓][39m Windows Version (10 Pro 64-bit, 22H2, 2009) [2.7s]

[32m[✓][39m Android toolchain - develop for Android devices (Android SDK version 36.0.0) [7.9s]
    [32m•[39m Android SDK at C:\Users\<USER>\AppData\Local\Android\sdk
    [32m•[39m Platform android-36, build-tools 36.0.0
    [32m•[39m Java binary at: C:\Program Files\Android\Android Studio\jbr\bin\java
      This is the JDK bundled with the latest Android Studio installation on this machine.
      To manually set the JDK path, use: `flutter config --jdk-dir="path/to/jdk"`.
    [32m•[39m Java version OpenJDK Runtime Environment (build 21.0.6+-13368085-b895.109)
    [32m•[39m All Android licenses accepted.

[31m[✗][39m Chrome - develop for the web (Cannot find Chrome executable at .\Google\Chrome\Application\chrome.exe) [334ms]
    [33m![39m Cannot find Chrome. Try setting CHROME_EXECUTABLE to a Chrome executable.

[33m[!][39m Visual Studio - develop Windows apps (Visual Studio Build Tools 2019 16.11.47) [332ms]
    [32m•[39m Visual Studio at C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools
    [32m•[39m Visual Studio Build Tools 2019 version 16.11.36107.64
    [31m✗[39m The current Visual Studio installation is incomplete.
      Please use Visual Studio Installer to complete the installation or reinstall Visual Studio.

[32m[✓][39m Android Studio (version 2024.3.2) [9ms]
    [32m•[39m Android Studio at C:\Program Files\Android\Android Studio
    [32m•[39m Flutter plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/9212-flutter
    [32m•[39m Dart plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/6351-dart
    [32m•[39m Java version OpenJDK Runtime Environment (build 21.0.6+-13368085-b895.109)

[32m[✓][39m VS Code (version 1.102.1) [6ms]
    [32m•[39m VS Code at C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code
    [32m•[39m Flutter extension version 3.114.0

[32m[✓][39m Connected device (3 available) [250ms]
    [32m•[39m sdk gphone64 x86 64 (mobile) • emulator-5554 • android-x64    • Android 16 (API 36) (emulator)
    [32m•[39m Windows (desktop)            • windows       • windows-x64    • Microsoft Windows [Version 10.0.19045.5965]
    [32m•[39m Edge (web)                   • edge          • web-javascript • Microsoft Edge 138.0.3351.95

[32m[✓][39m Network resources [1,228ms]
    [32m•[39m All expected network resources are available.

[33m![39m Doctor found issues in 2 categories.
```
