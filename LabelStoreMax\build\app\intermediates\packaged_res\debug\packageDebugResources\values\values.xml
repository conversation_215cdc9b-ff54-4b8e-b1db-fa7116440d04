<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">Velvete Store</string>
    <string name="default_web_client_id" translatable="false">655195009649-7jbth76ra51pat6odfjgas59305l7pdv.apps.googleusercontent.com</string>
    <string name="facebook_app_id">1234567890123456</string>
    <string name="facebook_client_token">your_facebook_client_token</string>
    <string name="fb_login_protocol_scheme">fb1234567890123456</string>
    <string name="gcm_defaultSenderId" translatable="false">655195009649</string>
    <string name="google_api_key" translatable="false">AIzaSyBHR8q_o0GQeJqZdZbbPRBN7lsVIcIler0</string>
    <string name="google_app_id" translatable="false">1:655195009649:android:e9b87d7213997fa76121d0</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyBHR8q_o0GQeJqZdZbbPRBN7lsVIcIler0</string>
    <string name="google_storage_bucket" translatable="false">velvete-store-01.firebasestorage.app</string>
    <string name="project_id" translatable="false">velvete-store-01</string>
    <style name="LaunchTheme" parent="Theme.AppCompat.Light.NoActionBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style>
    <style name="NormalTheme" parent="@android:style/Theme.Black.NoTitleBar">
        <item name="android:windowBackground">@android:color/white</item>
    </style>
</resources>