{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-113:/values-ko/values-ko.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\af486666b169eb5d50216ab5d4cc9553\\transformed\\biometric-1.1.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,156,242,346,463,571,687,793,906,1000,1120,1234", "endColumns": "100,85,103,116,107,115,105,112,93,119,113,102", "endOffsets": "151,237,341,458,566,682,788,901,995,1115,1229,1332"}, "to": {"startLines": "94,98,110,111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8834,9175,10138,10242,10359,10467,10583,10689,10802,10896,11016,11130", "endColumns": "100,85,103,116,107,115,105,112,93,119,113,102", "endOffsets": "8930,9256,10237,10354,10462,10578,10684,10797,10891,11011,11125,11228"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,249,314,378,447,521,600,683,789,864,917,979,1060,1122,1179,1266,1326,1384,1442,1501,1558,1612,1707,1763,1820,1874,1940,2044,2119,2191,2272,2350,2427,2548,2613,2678,2778,2857,2932,2982,3033,3099,3163,3233,3304,3375,3443,3514,3586,3656,3749,3829,3903,3983,4065,4137,4202,4274,4322,4395,4459,4534,4611,4673,4737,4800,4867,4951,5029,5109,5187,5241,5296,5368,5445,5518", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,64,63,68,73,78,82,105,74,52,61,80,61,56,86,59,57,57,58,56,53,94,55,56,53,65,103,74,71,80,77,76,120,64,64,99,78,74,49,50,65,63,69,70,70,67,70,71,69,92,79,73,79,81,71,64,71,47,72,63,74,76,61,63,62,66,83,77,79,77,53,54,71,76,72,70", "endOffsets": "244,309,373,442,516,595,678,784,859,912,974,1055,1117,1174,1261,1321,1379,1437,1496,1553,1607,1702,1758,1815,1869,1935,2039,2114,2186,2267,2345,2422,2543,2608,2673,2773,2852,2927,2977,3028,3094,3158,3228,3299,3370,3438,3509,3581,3651,3744,3824,3898,3978,4060,4132,4197,4269,4317,4390,4454,4529,4606,4668,4732,4795,4862,4946,5024,5104,5182,5236,5291,5363,5440,5513,5584"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3097,3162,3226,3295,3369,4125,4208,4314,9501,9554,9767,11294,11510,11567,11654,11714,11772,11830,11889,11946,12000,12095,12151,12208,12262,12328,12432,12507,12579,12660,12738,12815,12936,13001,13066,13166,13245,13320,13370,13421,13487,13551,13621,13692,13763,13831,13902,13974,14044,14137,14217,14291,14371,14453,14525,14590,14662,14710,14783,14847,14922,14999,15061,15125,15188,15255,15339,15417,15497,15575,15629,16044,16476,16553,16694", "endLines": "5,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "endColumns": "12,64,63,68,73,78,82,105,74,52,61,80,61,56,86,59,57,57,58,56,53,94,55,56,53,65,103,74,71,80,77,76,120,64,64,99,78,74,49,50,65,63,69,70,70,67,70,71,69,92,79,73,79,81,71,64,71,47,72,63,74,76,61,63,62,66,83,77,79,77,53,54,71,76,72,70", "endOffsets": "294,3157,3221,3290,3364,3443,4203,4309,4384,9549,9611,9843,11351,11562,11649,11709,11767,11825,11884,11941,11995,12090,12146,12203,12257,12323,12427,12502,12574,12655,12733,12810,12931,12996,13061,13161,13240,13315,13365,13416,13482,13546,13616,13687,13758,13826,13897,13969,14039,14132,14212,14286,14366,14448,14520,14585,14657,14705,14778,14842,14917,14994,15056,15120,15183,15250,15334,15412,15492,15570,15624,15679,16111,16548,16621,16760"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-ko\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "116", "endOffsets": "311"}, "to": {"startLines": "84", "startColumns": "4", "startOffsets": "7782", "endColumns": "120", "endOffsets": "7898"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-ko\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,428,540,638,749,862,965,1056,1197,1296,1428,1542,1656,1771,1826,1880", "endColumns": "99,134,111,97,110,112,102,90,140,98,131,113,113,114,54,53,70", "endOffsets": "292,427,539,637,748,861,964,1055,1196,1295,1427,1541,1655,1770,1825,1879,1950"}, "to": {"startLines": "76,77,78,79,80,81,82,83,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6887,6991,7130,7246,7348,7463,7580,7687,7903,8048,8151,8287,8405,8523,8642,8701,8759", "endColumns": "103,138,115,101,114,116,106,94,144,102,135,117,117,118,58,57,74", "endOffsets": "6986,7125,7241,7343,7458,7575,7682,7777,8043,8146,8282,8400,8518,8637,8696,8754,8829"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,126,196", "endColumns": "70,69,69", "endOffsets": "121,191,261"}, "to": {"startLines": "52,100,105", "startColumns": "4,4,4", "startOffsets": "4544,9351,9697", "endColumns": "70,69,69", "endOffsets": "4610,9416,9762"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,132,220,287,353,413,479", "endColumns": "76,87,66,65,59,65,61", "endOffsets": "127,215,282,348,408,474,536"}, "to": {"startLines": "198,199,200,201,202,203,204", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "16866,16943,17031,17098,17164,17224,17290", "endColumns": "76,87,66,65,59,65,61", "endOffsets": "16938,17026,17093,17159,17219,17285,17347"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\97b0e46e5034b62169defac2cb4fe8fb\\transformed\\preference-1.2.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,252,326,458,627,709", "endColumns": "65,80,73,131,168,81,75", "endOffsets": "166,247,321,453,622,704,780"}, "to": {"startLines": "95,104,185,189,519,525,526", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "8935,9616,15842,16116,42394,42976,43058", "endColumns": "65,80,73,131,168,81,75", "endOffsets": "8996,9692,15911,16243,42558,43053,43129"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,190,252,315,387,441,501,549,617,690,760,845,930,1006,1079,1152,1228,1311,1393,1456,1535,1611,1682,1773,1846,1925,1995,2081,2149,2227,2307,2383,2454,2537,2600,2668,2765,2855,2917,2978,3418,3836,3904,3997,4082,4137,4221,4301,4361,4441,4518,4573,4645,4690,4765,4852,4913,4981,5048,5114,5158,5224,5311,5385,5428,5471,5531,5589,5646,5762,5872,5965,6027,6094,6159,6239,6310,6379,6446,6520,6581,6660,6732,6809,6858,6952,6997,7047,7107,7165,7227,7287,7349,7422,7483,7530", "endColumns": "62,71,61,62,71,53,59,47,67,72,69,84,84,75,72,72,75,82,81,62,78,75,70,90,72,78,69,85,67,77,79,75,70,82,62,67,96,89,61,60,439,417,67,92,84,54,83,79,59,79,76,54,71,44,74,86,60,67,66,65,43,65,86,73,42,42,59,57,56,115,109,92,61,66,64,79,70,68,66,73,60,78,71,76,48,93,44,49,59,57,61,59,61,72,60,46,60", "endOffsets": "113,185,247,310,382,436,496,544,612,685,755,840,925,1001,1074,1147,1223,1306,1388,1451,1530,1606,1677,1768,1841,1920,1990,2076,2144,2222,2302,2378,2449,2532,2595,2663,2760,2850,2912,2973,3413,3831,3899,3992,4077,4132,4216,4296,4356,4436,4513,4568,4640,4685,4760,4847,4908,4976,5043,5109,5153,5219,5306,5380,5423,5466,5526,5584,5641,5757,5867,5960,6022,6089,6154,6234,6305,6374,6441,6515,6576,6655,6727,6804,6853,6947,6992,7042,7102,7160,7222,7282,7344,7417,7478,7525,7586"}, "to": {"startLines": "205,206,207,208,209,210,211,215,216,217,218,221,223,224,226,231,235,250,253,254,255,257,258,259,261,265,266,267,268,269,270,271,272,273,274,276,279,280,286,287,288,299,300,301,302,303,304,305,306,307,308,309,310,317,318,319,322,323,324,325,329,334,335,336,337,338,343,344,345,346,347,348,352,353,363,364,366,367,371,372,374,379,386,387,458,459,460,462,467,480,481,482,483,484,485,486,502", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17352,17415,17487,17549,17612,17684,17738,17991,18039,18107,18180,18379,18533,18618,18759,19088,19357,20321,20539,20621,20684,20823,20899,20970,21118,21389,21468,21538,21624,21692,21770,21850,21926,21997,22080,22216,22434,22531,23157,23219,23280,24472,24890,24958,25051,25136,25191,25275,25355,25415,25495,25572,25627,26119,26164,26239,26479,26540,26608,26675,27044,27366,27432,27519,27593,27636,27903,27963,28021,28078,28194,28304,28573,28635,29389,29454,29607,29678,29973,30040,30175,30638,31089,31161,37445,37494,37588,37718,38151,39585,39643,39705,39765,39827,39900,39961,41109", "endColumns": "62,71,61,62,71,53,59,47,67,72,69,84,84,75,72,72,75,82,81,62,78,75,70,90,72,78,69,85,67,77,79,75,70,82,62,67,96,89,61,60,439,417,67,92,84,54,83,79,59,79,76,54,71,44,74,86,60,67,66,65,43,65,86,73,42,42,59,57,56,115,109,92,61,66,64,79,70,68,66,73,60,78,71,76,48,93,44,49,59,57,61,59,61,72,60,46,60", "endOffsets": "17410,17482,17544,17607,17679,17733,17793,18034,18102,18175,18245,18459,18613,18689,18827,19156,19428,20399,20616,20679,20758,20894,20965,21056,21186,21463,21533,21619,21687,21765,21845,21921,21992,22075,22138,22279,22526,22616,23214,23275,23715,24885,24953,25046,25131,25186,25270,25350,25410,25490,25567,25622,25694,26159,26234,26321,26535,26603,26670,26736,27083,27427,27514,27588,27631,27674,27958,28016,28073,28189,28299,28392,28630,28697,29449,29529,29673,29742,30035,30109,30231,30712,31156,31233,37489,37583,37628,37763,38206,39638,39700,39760,39822,39895,39956,40003,41165"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,838,929,1022,1117,1211,1311,1404,1499,1593,1684,1775,1855,1953,2047,2142,2242,2339,2439,2591,2685", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "197,291,392,474,572,678,758,833,924,1017,1112,1206,1306,1399,1494,1588,1679,1770,1850,1948,2042,2137,2237,2334,2434,2586,2680,2759"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "299,396,490,591,673,771,877,957,1032,1123,1216,1311,1405,1505,1598,1693,1787,1878,1969,2049,2147,2241,2336,2436,2533,2633,2785,16397", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "391,485,586,668,766,872,952,1027,1118,1211,1306,1400,1500,1593,1688,1782,1873,1964,2044,2142,2236,2331,2431,2528,2628,2780,2874,16471"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,305,384,512,555,616,697,762,910,966,1042,1099,1152,1216,1285,1369,1548,1610,1670,1721,1772,1841,1945,2028,2083,2156,2229,2301,2362,2446,2623,2690,2764,2819,2877,2936,2994,3057,3126,3216,3296,3377,3468,3553,3624,3703,3788,3915,4051,4149,4247,4298,4632,4712,4769", "endColumns": "105,143,78,127,42,60,80,64,147,55,75,56,52,63,68,83,178,61,59,50,50,68,103,82,54,72,72,71,60,83,176,66,73,54,57,58,57,62,68,89,79,80,90,84,70,78,84,126,135,97,97,50,333,79,56,54", "endOffsets": "156,300,379,507,550,611,692,757,905,961,1037,1094,1147,1211,1280,1364,1543,1605,1665,1716,1767,1836,1940,2023,2078,2151,2224,2296,2357,2441,2618,2685,2759,2814,2872,2931,2989,3052,3121,3211,3291,3372,3463,3548,3619,3698,3783,3910,4046,4144,4242,4293,4627,4707,4764,4819"}, "to": {"startLines": "281,282,283,285,289,290,291,292,293,294,295,311,314,316,320,321,326,332,333,341,351,355,356,357,358,359,365,368,373,375,376,377,378,381,383,384,388,392,393,395,397,409,434,435,436,437,438,456,463,464,465,466,468,469,470,487", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "22621,22727,22871,23029,23720,23763,23824,23905,23970,24118,24174,25699,25911,26055,26326,26395,26741,27244,27306,27790,28522,28771,28840,28944,29027,29082,29534,29747,30114,30236,30320,30497,30564,30770,30914,30972,31238,31689,31752,31906,32083,33421,35445,35536,35621,35692,35771,37251,37768,37904,38002,38100,38211,38545,38625,40008", "endColumns": "105,143,78,127,42,60,80,64,147,55,75,56,52,63,68,83,178,61,59,50,50,68,103,82,54,72,72,71,60,83,176,66,73,54,57,58,57,62,68,89,79,80,90,84,70,78,84,126,135,97,97,50,333,79,56,54", "endOffsets": "22722,22866,22945,23152,23758,23819,23900,23965,24113,24169,24245,25751,25959,26114,26390,26474,26915,27301,27361,27836,28568,28835,28939,29022,29077,29150,29602,29814,30170,30315,30492,30559,30633,30820,30967,31026,31291,31747,31816,31991,32158,33497,35531,35616,35687,35766,35851,37373,37899,37997,38095,38146,38540,38620,38677,40058"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e3c1b17a9ffaafd0471e6527f4794f6b\\transformed\\jetified-credentials-1.5.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,109", "endOffsets": "158,268"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "2879,2987", "endColumns": "107,109", "endOffsets": "2982,3092"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,180,240,316,376,438,495,562,628,688,745", "endColumns": "68,55,59,75,59,61,56,66,65,59,56,62", "endOffsets": "119,175,235,311,371,433,490,557,623,683,740,803"}, "to": {"startLines": "220,230,232,233,234,238,246,249,252,256,260,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18310,19032,19161,19221,19297,19560,20074,20254,20473,20763,21061,21326", "endColumns": "68,55,59,75,59,61,56,66,65,59,56,62", "endOffsets": "18374,19083,19216,19292,19352,19617,20126,20316,20534,20818,21113,21384"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,141,234,337", "endColumns": "85,92,102,93", "endOffsets": "136,229,332,426"}, "to": {"startLines": "96,107,108,109", "startColumns": "4,4,4,4", "startOffsets": "9001,9848,9941,10044", "endColumns": "85,92,102,93", "endOffsets": "9082,9936,10039,10133"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\587767e501a9ab66a3f91617d285250f\\transformed\\core-1.16.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,828"}, "to": {"startLines": "40,41,42,43,44,45,46,197", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3448,3540,3640,3734,3831,3927,4025,16765", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "3535,3635,3729,3826,3922,4020,4120,16861"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca6706086df4d54b31c3004876d79fd4\\transformed\\jetified-facebook-login-18.0.3\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,310,429,538,618,702,777,862,948,1055,1157,1245,1333,1428,1541,1620,1699,1828,1922,2023,2129,2225", "endColumns": "134,119,118,108,79,83,74,84,85,106,101,87,87,94,112,78,78,128,93,100,105,95,101", "endOffsets": "185,305,424,533,613,697,772,857,943,1050,1152,1240,1328,1423,1536,1615,1694,1823,1917,2018,2124,2220,2322"}, "to": {"startLines": "53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4615,4750,4870,4989,5098,5178,5262,5337,5422,5508,5615,5717,5805,5893,5988,6101,6180,6259,6388,6482,6583,6689,6785", "endColumns": "134,119,118,108,79,83,74,84,85,106,101,87,87,94,112,78,78,128,93,100,105,95,101", "endOffsets": "4745,4865,4984,5093,5173,5257,5332,5417,5503,5610,5712,5800,5888,5983,6096,6175,6254,6383,6477,6578,6684,6780,6882"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "68", "endOffsets": "119"}, "to": {"startLines": "354", "startColumns": "4", "startOffsets": "28702", "endColumns": "68", "endOffsets": "28766"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,184,260,348,438,518,593,672,751,830,894,958,1031,1107,1175,1249,1313", "endColumns": "78,75,87,89,79,74,78,78,78,63,63,72,75,67,73,63,113", "endOffsets": "179,255,343,433,513,588,667,746,825,889,953,1026,1102,1170,1244,1308,1422"}, "to": {"startLines": "50,51,97,99,101,122,123,183,184,186,187,190,191,195,520,521,522", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4389,4468,9087,9261,9421,11356,11431,15684,15763,15916,15980,16248,16321,16626,42563,42637,42701", "endColumns": "78,75,87,89,79,74,78,78,78,63,63,72,75,67,73,63,113", "endOffsets": "4463,4539,9170,9346,9496,11426,11505,15758,15837,15975,16039,16316,16392,16689,42632,42696,42810"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,116,185,248,327,402,476,549,640,719,800,875,963,1016,1105,1163,1296,1443,1556,1641,1728,1813,1900,2017,2098,2187,2340,2430,2546,2721,2897,2986,3091,3213,3292,3349,3408,3480,3560,3640,3706,3786,3866,3953,4108,4172,4234,4297,4396,4472,4546,4619,4693,4761,4829,4929,5011,5111,5194,5260,5340,5404,5477,5533,5618,5714,5803,5910,5968,6047,6136,6255,6324,6391,6476,6527,6578,6640,6720,6850,7029,7227,7311,7379,7449,7509,7593,7691,7765,7833,7900,7968,8032,8107,8196,8292,8363,8425,8477,8602,8665,8719,8788,8851,8922,8991,9082,9173,9256,9330,9396,9465,9527", "endColumns": "60,68,62,78,74,73,72,90,78,80,74,87,52,88,57,132,146,112,84,86,84,86,116,80,88,152,89,115,174,175,88,104,121,78,56,58,71,79,79,65,79,79,86,154,63,61,62,98,75,73,72,73,67,67,99,81,99,82,65,79,63,72,55,84,95,88,106,57,78,88,118,68,66,84,50,50,61,79,129,178,197,83,67,69,59,83,97,73,67,66,67,63,74,88,95,70,61,51,124,62,53,68,62,70,68,90,90,82,73,65,68,61,121", "endOffsets": "111,180,243,322,397,471,544,635,714,795,870,958,1011,1100,1158,1291,1438,1551,1636,1723,1808,1895,2012,2093,2182,2335,2425,2541,2716,2892,2981,3086,3208,3287,3344,3403,3475,3555,3635,3701,3781,3861,3948,4103,4167,4229,4292,4391,4467,4541,4614,4688,4756,4824,4924,5006,5106,5189,5255,5335,5399,5472,5528,5613,5709,5798,5905,5963,6042,6131,6250,6319,6386,6471,6522,6573,6635,6715,6845,7024,7222,7306,7374,7444,7504,7588,7686,7760,7828,7895,7963,8027,8102,8191,8287,8358,8420,8472,8597,8660,8714,8783,8846,8917,8986,9077,9168,9251,9325,9391,9460,9522,9644"}, "to": {"startLines": "212,213,214,284,296,297,298,315,328,330,331,362,380,382,385,389,390,391,394,396,398,399,400,401,402,403,404,405,406,407,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,457,461,471,472,473,474,475,476,477,478,479,488,489,490,491,492,493,494,495,496,497,498,499,500,501,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17798,17859,17928,22950,24250,24325,24399,25964,26965,27088,27169,29301,30717,30825,31031,31296,31429,31576,31821,31996,32163,32248,32335,32452,32533,32622,32775,32865,32981,33156,33332,33502,33607,33729,33808,33865,33924,33996,34076,34156,34222,34302,34382,34469,34624,34688,34750,34813,34912,34988,35062,35135,35209,35277,35345,35856,35938,36038,36121,36187,36267,36331,36404,36460,36545,36641,36730,36837,36895,36974,37063,37182,37378,37633,38682,38733,38784,38846,38926,39056,39235,39433,39517,40063,40133,40193,40277,40375,40449,40517,40584,40652,40716,40791,40880,40976,41047,41170,41222,41347,41410,41464,41533,41596,41667,41736,41827,41918,42001,42075,42141,42210,42272", "endColumns": "60,68,62,78,74,73,72,90,78,80,74,87,52,88,57,132,146,112,84,86,84,86,116,80,88,152,89,115,174,175,88,104,121,78,56,58,71,79,79,65,79,79,86,154,63,61,62,98,75,73,72,73,67,67,99,81,99,82,65,79,63,72,55,84,95,88,106,57,78,88,118,68,66,84,50,50,61,79,129,178,197,83,67,69,59,83,97,73,67,66,67,63,74,88,95,70,61,51,124,62,53,68,62,70,68,90,90,82,73,65,68,61,121", "endOffsets": "17854,17923,17986,23024,24320,24394,24467,26050,27039,27164,27239,29384,30765,30909,31084,31424,31571,31684,31901,32078,32243,32330,32447,32528,32617,32770,32860,32976,33151,33327,33416,33602,33724,33803,33860,33919,33991,34071,34151,34217,34297,34377,34464,34619,34683,34745,34808,34907,34983,35057,35130,35204,35272,35340,35440,35933,36033,36116,36182,36262,36326,36399,36455,36540,36636,36725,36832,36890,36969,37058,37177,37246,37440,37713,38728,38779,38841,38921,39051,39230,39428,39512,39580,40128,40188,40272,40370,40444,40512,40579,40647,40711,40786,40875,40971,41042,41104,41217,41342,41405,41459,41528,41591,41662,41731,41822,41913,41996,42070,42136,42205,42267,42389"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,184,249,325,390,449,515,576,636,710,776,842,900,967,1028,1093,1151,1220,1279,1355,1428,1500,1578,1656,1733,1778,1823,1889,1951,2011,2076,2153,2222,2299", "endColumns": "59,68,64,75,64,58,65,60,59,73,65,65,57,66,60,64,57,68,58,75,72,71,77,77,76,44,44,65,61,59,64,76,68,76,76", "endOffsets": "110,179,244,320,385,444,510,571,631,705,771,837,895,962,1023,1088,1146,1215,1274,1350,1423,1495,1573,1651,1728,1773,1818,1884,1946,2006,2071,2148,2217,2294,2371"}, "to": {"startLines": "219,222,225,227,228,229,236,237,239,240,241,242,243,244,245,247,248,251,262,263,275,277,278,312,313,327,339,340,342,349,350,360,361,369,370", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18250,18464,18694,18832,18908,18973,19433,19499,19622,19682,19756,19822,19888,19946,20013,20131,20196,20404,21191,21250,22143,22284,22356,25756,25834,26920,27679,27724,27841,28397,28457,29155,29232,29819,29896", "endColumns": "59,68,64,75,64,58,65,60,59,73,65,65,57,66,60,64,57,68,58,75,72,71,77,77,76,44,44,65,61,59,64,76,68,76,76", "endOffsets": "18305,18528,18754,18903,18968,19027,19494,19555,19677,19751,19817,19883,19941,20008,20069,20191,20249,20468,21245,21321,22211,22351,22429,25829,25906,26960,27719,27785,27898,28452,28517,29227,29296,29891,29968"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,137", "endColumns": "81,78", "endOffsets": "132,211"}, "to": {"startLines": "523,524", "startColumns": "4,4", "startOffsets": "42815,42897", "endColumns": "81,78", "endOffsets": "42892,42971"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-ko\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,65", "endOffsets": "258,324"}, "to": {"startLines": "120,527", "startColumns": "4,4", "startOffsets": "11233,43134", "endColumns": "60,69", "endOffsets": "11289,43199"}}]}]}