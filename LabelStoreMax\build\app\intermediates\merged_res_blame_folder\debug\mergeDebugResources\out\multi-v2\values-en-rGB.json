{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-113:/values-en-rGB/values-en-rGB.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,595,679,777,892,971,1031,1096,1186,1253,1312,1402,1466,1530,1593,1662,1726,1780,1892,1950,2012,2066,2138,2260,2347,2422,2513,2594,2675,2815,2892,2973,3100,3191,3268,3322,3373,3439,3509,3586,3657,3732,3803,3880,3949,4018,4125,4216,4288,4377,4466,4540,4612,4698,4748,4827,4893,4973,5057,5119,5183,5246,5315,5415,5510,5602,5694,5752,5807,5885,5966,6041", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,81,77,76,85,83,97,114,78,59,64,89,66,58,89,63,63,62,68,63,53,111,57,61,53,71,121,86,74,90,80,80,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,77,80,74,74", "endOffsets": "267,349,427,504,590,674,772,887,966,1026,1091,1181,1248,1307,1397,1461,1525,1588,1657,1721,1775,1887,1945,2007,2061,2133,2255,2342,2417,2508,2589,2670,2810,2887,2968,3095,3186,3263,3317,3368,3434,3504,3581,3652,3727,3798,3875,3944,4013,4120,4211,4283,4372,4461,4535,4607,4693,4743,4822,4888,4968,5052,5114,5178,5241,5310,5410,5505,5597,5689,5747,5802,5880,5961,6036,6111"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,83,84,87,102,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,169,174,175,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3205,3287,3365,3442,3528,4331,4429,4544,8253,8313,8541,10244,10482,10541,10631,10695,10759,10822,10891,10955,11009,11121,11179,11241,11295,11367,11489,11576,11651,11742,11823,11904,12044,12121,12202,12329,12420,12497,12551,12602,12668,12738,12815,12886,12961,13032,13109,13178,13247,13354,13445,13517,13606,13695,13769,13841,13927,13977,14056,14122,14202,14286,14348,14412,14475,14544,14644,14739,14831,14923,14981,15412,15867,15948,16096", "endLines": "5,35,36,37,38,39,47,48,49,83,84,87,102,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,169,174,175,177", "endColumns": "12,81,77,76,85,83,97,114,78,59,64,89,66,58,89,63,63,62,68,63,53,111,57,61,53,71,121,86,74,90,80,80,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,77,80,74,74", "endOffsets": "317,3282,3360,3437,3523,3607,4424,4539,4618,8308,8373,8626,10306,10536,10626,10690,10754,10817,10886,10950,11004,11116,11174,11236,11290,11362,11484,11571,11646,11737,11818,11899,12039,12116,12197,12324,12415,12492,12546,12597,12663,12733,12810,12881,12956,13027,13104,13173,13242,13349,13440,13512,13601,13690,13764,13836,13922,13972,14051,14117,14197,14281,14343,14407,14470,14539,14639,14734,14826,14918,14976,15031,15485,15943,16018,16166"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-en-rGB\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "133", "endOffsets": "332"}, "to": {"startLines": "65", "startColumns": "4", "startOffsets": "6360", "endColumns": "137", "endOffsets": "6493"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\97b0e46e5034b62169defac2cb4fe8fb\\transformed\\preference-1.2.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,173,260,334,468,637,717", "endColumns": "67,86,73,133,168,79,75", "endOffsets": "168,255,329,463,632,712,788"}, "to": {"startLines": "76,85,166,170,499,505,506", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7643,8378,15209,15490,46416,47020,47100", "endColumns": "67,86,73,133,168,79,75", "endOffsets": "7706,8460,15278,15619,46580,47095,47171"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,264,333,411,469,528,578,658,741,825,923,1021,1107,1185,1264,1347,1442,1535,1602,1689,1776,1866,1976,2057,2144,2225,2328,2408,2504,2593,2678,2766,2873,2951,3033,3137,3233,3305,3370,4040,4686,4763,4882,4986,5041,5138,5229,5296,5388,5482,5539,5623,5672,5751,5850,5927,5997,6064,6130,6177,6257,6350,6426,6471,6516,6586,6644,6705,6882,7054,7178,7243,7328,7406,7500,7584,7670,7746,7835,7911,8008,8085,8174,8225,8353,8402,8456,8523,8586,8654,8721,8792,8879,8944,8993", "endColumns": "68,77,61,68,77,57,58,49,79,82,83,97,97,85,77,78,82,94,92,66,86,86,89,109,80,86,80,102,79,95,88,84,87,106,77,81,103,95,71,64,669,645,76,118,103,54,96,90,66,91,93,56,83,48,78,98,76,69,66,65,46,79,92,75,44,44,69,57,60,176,171,123,64,84,77,93,83,85,75,88,75,96,76,88,50,127,48,53,66,62,67,66,70,86,64,48,74", "endOffsets": "119,197,259,328,406,464,523,573,653,736,820,918,1016,1102,1180,1259,1342,1437,1530,1597,1684,1771,1861,1971,2052,2139,2220,2323,2403,2499,2588,2673,2761,2868,2946,3028,3132,3228,3300,3365,4035,4681,4758,4877,4981,5036,5133,5224,5291,5383,5477,5534,5618,5667,5746,5845,5922,5992,6059,6125,6172,6252,6345,6421,6466,6511,6581,6639,6700,6877,7049,7173,7238,7323,7401,7495,7579,7665,7741,7830,7906,8003,8080,8169,8220,8348,8397,8451,8518,8581,8649,8716,8787,8874,8939,8988,9063"}, "to": {"startLines": "186,187,188,189,190,191,192,196,197,198,199,202,204,205,207,212,216,231,234,235,236,238,239,240,242,246,247,248,249,250,251,252,253,254,255,257,260,261,267,268,269,280,281,282,283,284,285,286,287,288,289,290,291,298,299,300,303,304,305,306,310,315,316,317,318,319,324,325,326,327,328,329,333,334,343,344,346,347,351,352,354,359,366,367,438,439,440,442,447,460,461,462,463,464,465,466,482", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16791,16860,16938,17000,17069,17147,17205,17491,17541,17621,17704,17931,18107,18205,18359,18708,19000,20024,20261,20354,20421,20575,20662,20752,20923,21220,21307,21388,21491,21571,21667,21756,21841,21929,22036,22191,22448,22552,23298,23370,23435,25034,25680,25757,25876,25980,26035,26132,26223,26290,26382,26476,26533,27113,27162,27241,27504,27581,27651,27718,28205,28588,28668,28761,28837,28882,29166,29236,29294,29355,29532,29704,30017,30082,30930,31008,31182,31266,31629,31705,31859,32449,32962,33039,40221,40272,40400,40547,41078,43151,43214,43282,43349,43420,43507,43572,44884", "endColumns": "68,77,61,68,77,57,58,49,79,82,83,97,97,85,77,78,82,94,92,66,86,86,89,109,80,86,80,102,79,95,88,84,87,106,77,81,103,95,71,64,669,645,76,118,103,54,96,90,66,91,93,56,83,48,78,98,76,69,66,65,46,79,92,75,44,44,69,57,60,176,171,123,64,84,77,93,83,85,75,88,75,96,76,88,50,127,48,53,66,62,67,66,70,86,64,48,74", "endOffsets": "16855,16933,16995,17064,17142,17200,17259,17536,17616,17699,17783,18024,18200,18286,18432,18782,19078,20114,20349,20416,20503,20657,20747,20857,20999,21302,21383,21486,21566,21662,21751,21836,21924,22031,22109,22268,22547,22643,23365,23430,24100,25675,25752,25871,25975,26030,26127,26218,26285,26377,26471,26528,26612,27157,27236,27335,27576,27646,27713,27779,28247,28663,28756,28832,28877,28922,29231,29289,29350,29527,29699,29823,30077,30162,31003,31097,31261,31347,31700,31789,31930,32541,33034,33123,40267,40395,40444,40596,41140,43209,43277,43344,43415,43502,43567,43616,44954"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,163,322,420,617,662,732,833,905,1150,1210,1301,1368,1423,1487,1561,1651,1935,2010,2076,2129,2182,2269,2398,2507,2564,2650,2730,2813,2878,2972,3243,3317,3392,3453,3513,3573,3633,3699,3773,3874,3962,4046,4144,4236,4309,4388,4473,4658,4852,4959,5080,5135,5841,5936,5997", "endColumns": "107,158,97,196,44,69,100,71,244,59,90,66,54,63,73,89,283,74,65,52,52,86,128,108,56,85,79,82,64,93,270,73,74,60,59,59,59,65,73,100,87,83,97,91,72,78,84,184,193,106,120,54,705,94,60,54", "endOffsets": "158,317,415,612,657,727,828,900,1145,1205,1296,1363,1418,1482,1556,1646,1930,2005,2071,2124,2177,2264,2393,2502,2559,2645,2725,2808,2873,2967,3238,3312,3387,3448,3508,3568,3628,3694,3768,3869,3957,4041,4139,4231,4304,4383,4468,4653,4847,4954,5075,5130,5836,5931,5992,6047"}, "to": {"startLines": "262,263,264,266,270,271,272,273,274,275,276,292,295,297,301,302,307,313,314,322,332,335,336,337,338,339,345,348,353,355,356,357,358,361,363,364,368,372,373,375,377,389,414,415,416,417,418,436,443,444,445,446,448,449,450,467", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "22648,22756,22915,23101,24105,24150,24220,24321,24393,24638,24698,26617,26862,27049,27340,27414,27784,28447,28522,29049,29964,30167,30254,30383,30492,30549,31102,31352,31794,31935,32029,32300,32374,32605,32779,32839,33128,33686,33752,33912,34115,35728,38005,38103,38195,38268,38347,39953,40601,40795,40902,41023,41145,41851,41946,43621", "endColumns": "107,158,97,196,44,69,100,71,244,59,90,66,54,63,73,89,283,74,65,52,52,86,128,108,56,85,79,82,64,93,270,73,74,60,59,59,59,65,73,100,87,83,97,91,72,78,84,184,193,106,120,54,705,94,60,54", "endOffsets": "22751,22910,23008,23293,24145,24215,24316,24388,24633,24693,24784,26679,26912,27108,27409,27499,28063,28517,28583,29097,30012,30249,30378,30487,30544,30630,31177,31430,31854,32024,32295,32369,32444,32661,32834,32894,33183,33747,33821,34008,34198,35807,38098,38190,38263,38342,38427,40133,40790,40897,41018,41073,41846,41941,42002,43671"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,207,282,370,454,535,615,747,835,938,1030,1140,1199,1312,1375,1543,1727,1873,1959,2061,2153,2251,2421,2522,2618,2856,2967,3094,3283,3496,3586,3697,3879,3973,4033,4097,4179,4273,4364,4436,4530,4614,4718,4913,4979,5045,5119,5230,5304,5388,5450,5526,5595,5663,5779,5865,5969,6063,6135,6214,6282,6368,6426,6526,6634,6732,6852,6912,7004,7106,7229,7300,7383,7481,7536,7589,7665,7762,7917,8168,8444,8548,8625,8706,8776,8883,9002,9085,9159,9227,9307,9391,9482,9587,9700,9766,9833,9888,10048,10116,10174,10254,10327,10405,10479,10598,10721,10831,10917,10989,11073,11139", "endColumns": "68,82,74,87,83,80,79,131,87,102,91,109,58,112,62,167,183,145,85,101,91,97,169,100,95,237,110,126,188,212,89,110,181,93,59,63,81,93,90,71,93,83,103,194,65,65,73,110,73,83,61,75,68,67,115,85,103,93,71,78,67,85,57,99,107,97,119,59,91,101,122,70,82,97,54,52,75,96,154,250,275,103,76,80,69,106,118,82,73,67,79,83,90,104,112,65,66,54,159,67,57,79,72,77,73,118,122,109,85,71,83,65,150", "endOffsets": "119,202,277,365,449,530,610,742,830,933,1025,1135,1194,1307,1370,1538,1722,1868,1954,2056,2148,2246,2416,2517,2613,2851,2962,3089,3278,3491,3581,3692,3874,3968,4028,4092,4174,4268,4359,4431,4525,4609,4713,4908,4974,5040,5114,5225,5299,5383,5445,5521,5590,5658,5774,5860,5964,6058,6130,6209,6277,6363,6421,6521,6629,6727,6847,6907,6999,7101,7224,7295,7378,7476,7531,7584,7660,7757,7912,8163,8439,8543,8620,8701,8771,8878,8997,9080,9154,9222,9302,9386,9477,9582,9695,9761,9828,9883,10043,10111,10169,10249,10322,10400,10474,10593,10716,10826,10912,10984,11068,11134,11285"}, "to": {"startLines": "193,194,195,265,277,278,279,296,309,311,312,342,360,362,365,369,370,371,374,376,378,379,380,381,382,383,384,385,386,387,388,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,437,441,451,452,453,454,455,456,457,458,459,468,469,470,471,472,473,474,475,476,477,478,479,480,481,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17264,17333,17416,23013,24789,24873,24954,26917,28117,28252,28355,30820,32546,32666,32899,33188,33356,33540,33826,34013,34203,34295,34393,34563,34664,34760,34998,35109,35236,35425,35638,35812,35923,36105,36199,36259,36323,36405,36499,36590,36662,36756,36840,36944,37139,37205,37271,37345,37456,37530,37614,37676,37752,37821,37889,38432,38518,38622,38716,38788,38867,38935,39021,39079,39179,39287,39385,39505,39565,39657,39759,39882,40138,40449,42007,42062,42115,42191,42288,42443,42694,42970,43074,43676,43757,43827,43934,44053,44136,44210,44278,44358,44442,44533,44638,44751,44817,44959,45014,45174,45242,45300,45380,45453,45531,45605,45724,45847,45957,46043,46115,46199,46265", "endColumns": "68,82,74,87,83,80,79,131,87,102,91,109,58,112,62,167,183,145,85,101,91,97,169,100,95,237,110,126,188,212,89,110,181,93,59,63,81,93,90,71,93,83,103,194,65,65,73,110,73,83,61,75,68,67,115,85,103,93,71,78,67,85,57,99,107,97,119,59,91,101,122,70,82,97,54,52,75,96,154,250,275,103,76,80,69,106,118,82,73,67,79,83,90,104,112,65,66,54,159,67,57,79,72,77,73,118,122,109,85,71,83,65,150", "endOffsets": "17328,17411,17486,23096,24868,24949,25029,27044,28200,28350,28442,30925,32600,32774,32957,33351,33535,33681,33907,34110,34290,34388,34558,34659,34755,34993,35104,35231,35420,35633,35723,35918,36100,36194,36254,36318,36400,36494,36585,36657,36751,36835,36939,37134,37200,37266,37340,37451,37525,37609,37671,37747,37816,37884,38000,38513,38617,38711,38783,38862,38930,39016,39074,39174,39282,39380,39500,39560,39652,39754,39877,39948,40216,40542,42057,42110,42186,42283,42438,42689,42965,43069,43146,43752,43822,43929,44048,44131,44205,44273,44353,44437,44528,44633,44746,44812,44879,45009,45169,45237,45295,45375,45448,45526,45600,45719,45842,45952,46038,46110,46194,46260,46411"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\af486666b169eb5d50216ab5d4cc9553\\transformed\\biometric-1.1.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,251,370,499,637,764,882,1013,1113,1239,1378", "endColumns": "105,89,118,128,137,126,117,130,99,125,138,119", "endOffsets": "156,246,365,494,632,759,877,1008,1108,1234,1373,1493"}, "to": {"startLines": "75,79,91,92,93,94,95,96,97,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7537,7903,8936,9055,9184,9322,9449,9567,9698,9798,9924,10063", "endColumns": "105,89,118,128,137,126,117,130,99,125,138,119", "endOffsets": "7638,7988,9050,9179,9317,9444,9562,9693,9793,9919,10058,10178"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,192,257,342,405,474,533,608,678,745,806", "endColumns": "77,58,64,84,62,68,58,74,69,66,60,66", "endOffsets": "128,187,252,337,400,469,528,603,673,740,801,868"}, "to": {"startLines": "201,211,213,214,215,219,227,230,233,237,241,245", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17853,18649,18787,18852,18937,19221,19751,19949,20191,20508,20862,21153", "endColumns": "77,58,64,84,62,68,58,74,69,66,60,66", "endOffsets": "17926,18703,18847,18932,18995,19285,19805,20019,20256,20570,20918,21215"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\587767e501a9ab66a3f91617d285250f\\transformed\\core-1.16.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "40,41,42,43,44,45,46,178", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3612,3708,3810,3909,4008,4112,4215,16171", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "3703,3805,3904,4003,4107,4210,4326,16267"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "77,88,89,90", "startColumns": "4,4,4,4", "startOffsets": "7711,8631,8728,8837", "endColumns": "97,96,108,98", "endOffsets": "7804,8723,8832,8931"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-en-rGB\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,446,567,670,817,936,1048,1147,1302,1403,1551,1672,1814,1958,2017,2075", "endColumns": "100,147,120,102,146,118,111,98,154,100,147,120,141,143,58,57,74", "endOffsets": "297,445,566,669,816,935,1047,1146,1301,1402,1550,1671,1813,1957,2016,2074,2149"}, "to": {"startLines": "57,58,59,60,61,62,63,64,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5378,5483,5635,5760,5867,6018,6141,6257,6498,6657,6762,6914,7039,7185,7333,7396,7458", "endColumns": "104,151,124,106,150,122,115,102,158,104,151,124,145,147,62,61,78", "endOffsets": "5478,5630,5755,5862,6013,6136,6252,6355,6652,6757,6909,7034,7180,7328,7391,7453,7532"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e3c1b17a9ffaafd0471e6527f4794f6b\\transformed\\jetified-credentials-1.5.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,113", "endOffsets": "162,276"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "2979,3091", "endColumns": "111,113", "endOffsets": "3086,3200"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca6706086df4d54b31c3004876d79fd4\\transformed\\jetified-facebook-login-18.0.3\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,190,302,410", "endColumns": "134,111,107,147", "endOffsets": "185,297,405,553"}, "to": {"startLines": "53,54,55,56", "startColumns": "4,4,4,4", "startOffsets": "4875,5010,5122,5230", "endColumns": "134,111,107,147", "endOffsets": "5005,5117,5225,5373"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,207", "endColumns": "77,73,75", "endOffsets": "128,202,278"}, "to": {"startLines": "52,81,86", "startColumns": "4,4,4", "startOffsets": "4797,8092,8465", "endColumns": "77,73,75", "endOffsets": "4870,8161,8536"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,373,472,559,641,730,819,903,968,1032,1110,1192,1265,1342,1408", "endColumns": "91,81,93,98,86,81,88,88,83,64,63,77,81,72,76,65,120", "endOffsets": "192,274,368,467,554,636,725,814,898,963,1027,1105,1187,1260,1337,1403,1524"}, "to": {"startLines": "50,51,78,80,82,103,104,164,165,167,168,171,172,176,500,501,502", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4623,4715,7809,7993,8166,10311,10393,15036,15125,15283,15348,15624,15702,16023,46585,46662,46728", "endColumns": "91,81,93,98,86,81,88,88,83,64,63,77,81,72,76,65,120", "endOffsets": "4710,4792,7898,8087,8248,10388,10477,15120,15204,15343,15407,15697,15779,16091,46657,46723,46844"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-en-rGB\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "206,263", "endColumns": "56,70", "endOffsets": "262,333"}, "to": {"startLines": "101,507", "startColumns": "4,4", "startOffsets": "10183,47176", "endColumns": "60,74", "endOffsets": "10239,47246"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,137,235,301,369,433,506", "endColumns": "81,97,65,67,63,72,67", "endOffsets": "132,230,296,364,428,501,569"}, "to": {"startLines": "179,180,181,182,183,184,185", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "16272,16354,16452,16518,16586,16650,16723", "endColumns": "81,97,65,67,63,72,67", "endOffsets": "16349,16447,16513,16581,16645,16718,16786"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,84", "endOffsets": "136,221"}, "to": {"startLines": "503,504", "startColumns": "4,4", "startOffsets": "46849,46935", "endColumns": "85,84", "endOffsets": "46930,47015"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,173", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,426,526,634,718,818,933,1011,1086,1177,1270,1365,1459,1559,1652,1747,1841,1932,2023,2105,2208,2311,2410,2515,2619,2723,2879,15784", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "421,521,629,713,813,928,1006,1081,1172,1265,1360,1454,1554,1647,1742,1836,1927,2018,2100,2203,2306,2405,2510,2614,2718,2874,2974,15862"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,198,266,348,417,478,549,616,678,746,816,876,939,1013,1077,1153,1216,1288,1351,1437,1514,1595,1689,1785,1867,1916,1963,2038,2102,2169,2238,2340,2423,2521", "endColumns": "64,77,67,81,68,60,70,66,61,67,69,59,62,73,63,75,62,71,62,85,76,80,93,95,81,48,46,74,63,66,68,101,82,97,95", "endOffsets": "115,193,261,343,412,473,544,611,673,741,811,871,934,1008,1072,1148,1211,1283,1346,1432,1509,1590,1684,1780,1862,1911,1958,2033,2097,2164,2233,2335,2418,2516,2612"}, "to": {"startLines": "200,203,206,208,209,210,217,218,220,221,222,223,224,225,226,228,229,232,243,244,256,258,259,293,294,308,320,321,323,330,331,340,341,349,350", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17788,18029,18291,18437,18519,18588,19083,19154,19290,19352,19420,19490,19550,19613,19687,19810,19886,20119,21004,21067,22114,22273,22354,26684,26780,28068,28927,28974,29102,29828,29895,30635,30737,31435,31533", "endColumns": "64,77,67,81,68,60,70,66,61,67,69,59,62,73,63,75,62,71,62,85,76,80,93,95,81,48,46,74,63,66,68,101,82,97,95", "endOffsets": "17848,18102,18354,18514,18583,18644,19149,19216,19347,19415,19485,19545,19608,19682,19746,19881,19944,20186,21062,21148,22186,22349,22443,26775,26857,28112,28969,29044,29161,29890,29959,30732,30815,31528,31624"}}]}]}