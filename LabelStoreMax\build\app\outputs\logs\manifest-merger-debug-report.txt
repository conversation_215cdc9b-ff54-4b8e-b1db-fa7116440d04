-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:2:1-75:12
MERGED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:2:1-75:12
INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml:1:1-9:12
INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml:1:1-9:12
INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml:1:1-9:12
MERGED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-30:12
MERGED from [:flutter_timezone] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_timezone\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:google_sign_in_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:package_info_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\package_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:shared_preferences_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:stripe_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\stripe_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.android:facebook-android-sdk:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b9715a8446a004a2a156cc04b2f2340\transformed\jetified-facebook-android-sdk-17.0.2\AndroidManifest.xml:9:1-19:12
MERGED from [:flutter_facebook_auth] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_facebook_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.android:facebook-login:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca6706086df4d54b31c3004876d79fd4\transformed\jetified-facebook-login-18.0.3\AndroidManifest.xml:9:1-17:12
MERGED from [:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-34:12
MERGED from [:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-14:12
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-48:12
MERGED from [:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-48:12
MERGED from [:google_maps_flutter_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\google_maps_flutter_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_secure_storage] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_secure_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:geocoding_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geocoding_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:path_provider_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:sign_in_with_apple] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\sign_in_with_apple\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:sqflite_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.webkit:webkit:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acea059c377a46540689cab85dde90cd\transformed\webkit-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:2:1-77:12
MERGED from [com.facebook.android:facebook-gamingservices:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d50dddd3a008ed5d7fd91991e7962fa5\transformed\jetified-facebook-gamingservices-17.0.2\AndroidManifest.xml:9:1-19:12
MERGED from [com.facebook.android:facebook-share:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d6ffbb5596bdc2e4008a62c747de56ef\transformed\jetified-facebook-share-17.0.2\AndroidManifest.xml:9:1-19:12
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:9:1-42:12
MERGED from [com.stripe:stripe-android:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7cd974a17593dc6d6688e8906d100e3f\transformed\jetified-stripe-android-21.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:2:1-83:12
MERGED from [com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a77bf49bd582bfd1c6ec842fafdd01ea\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:2:1-14:12
MERGED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:2:1-73:12
MERGED from [com.stripe:stripe-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b05c15e4c638bbe5f218480c9cf74bd\transformed\jetified-stripe-ui-core-21.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.stripe:payments-model:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96a01676df5203a3b615fbb3718261a\transformed\jetified-payments-model-21.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.stripe:stripe-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\transformed\jetified-stripe-core-21.6.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3c723f32cc31c3d5ce9263682ffa8b7\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a3a4a579b2ef5f1ea25f0994a4694df\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d63c9f981e85b091b6acb23f12ae70\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a787c0803660539a92495da5d31ea17e\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb71e3b60d9eed6cadfc1a249b13d149\transformed\media-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:17:1-52:12
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08d12d7e609da5f0d48d54670ec0b8e6\transformed\jetified-googleid-1.1.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.credentials:credentials:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3c1b17a9ffaafd0471e6527f4794f6b\transformed\jetified-credentials-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af486666b169eb5d50216ab5d4cc9553\transformed\biometric-1.1.0\AndroidManifest.xml:17:1-29:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97b0e46e5034b62169defac2cb4fe8fb\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c650d55ab1e3e720524d3af207cfc6f\transformed\navigation-common-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c34a503dc6d29c2c14429a9f85ae2807\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc8f54f5d96ca582d8e67ebc5a19ac09\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d68662e109539e5ec3b632e8f6d8c967\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14daecc3ebd000f4593d0172df64931b\transformed\jetified-navigation-compose-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bfe8956b361855791638c0b764997d4e\transformed\jetified-activity-compose-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8057826fcdfd1bf2f63ffb4797b5d13\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:2:1-14:12
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\458b5494eafd4996cd399e5b49a9df4c\transformed\jetified-accompanist-systemuicontroller-0.34.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.pay.button:compose-pay-button:0.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f46722f2ae74414dc7e075cd66b8597\transformed\jetified-compose-pay-button-0.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-themeadapter-appcompat:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ae34fd4eb7d9b72497105893ff93e83\transformed\jetified-accompanist-themeadapter-appcompat-0.34.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.accompanist:accompanist-themeadapter-material:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01336241c67244a97fb4d45728317cf6\transformed\jetified-accompanist-themeadapter-material-0.34.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.accompanist:accompanist-themeadapter-material3:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23dae27af0289147ae3cee436f1f9c59\transformed\jetified-accompanist-themeadapter-material3-0.34.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.material3:material3:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb54734c218ce176bffcbd48481aca79\transformed\jetified-material3-1.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e34cf6b06a41f14fb0ceaffd38f45e53\transformed\jetified-material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2985daea627f0dec971a8ff5a48e644a\transformed\jetified-material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\292fe153afec31a88f60505c2f9c1809\transformed\jetified-material-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-themeadapter-core:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2120daa35dbbaf918041c277632ab66b\transformed\jetified-accompanist-themeadapter-core-0.34.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.accompanist:accompanist-flowlayout:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a646fcf2c5427a40a6bfed16eecfa6e3\transformed\jetified-accompanist-flowlayout-0.34.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25e6373e571a19f98d64d75f1f32810\transformed\jetified-animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a9760ed3571a1c2428d4c5ae6df07a4\transformed\jetified-animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30287e20ba85be6ba7bac4fc26404cb6\transformed\jetified-foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\732d08034aa94dff057adb759b93ca56\transformed\jetified-foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43052af7f9775fbf90804a1b628e4dad\transformed\jetified-ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b6c8d7cc1af8f2ca2f5d6b3f9f1ec167\transformed\jetified-ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16a42088a31edaba552ff082f0660291\transformed\jetified-ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d450bae7c21a07703659a23e148691d\transformed\jetified-ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32eacbaf6163f6900b1209ad53185332\transformed\jetified-ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b154d7bdb63c654ee93416929c1f009d\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.facebook.android:facebook-applinks:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfd4f5a9190f985b2f0947ef3a0bf3bc\transformed\jetified-facebook-applinks-17.0.2\AndroidManifest.xml:9:1-19:12
MERGED from [com.facebook.android:facebook-messenger:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb00181aad37f40671409b5fc3cb8167\transformed\jetified-facebook-messenger-17.0.2\AndroidManifest.xml:9:1-19:12
MERGED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:9:1-53:12
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6dfcec072e49025e6fe7486ae8e78b5\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be2b43e4377e03d598e671e01a23c196\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99f04d5ed1f58b1cb06a2785f5ce6b91\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5d2f88374dd98138a4945549e1432a2\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f48494b431258a5a46659ee4e96a961\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71234f4db869dea16b06e87b8a83d62f\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-wallet:19.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57d1206001d3e5c2badfd9d4afb4491b\transformed\jetified-play-services-wallet-19.4.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bda770351327ba3421df9753387b33\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:17:1-44:12
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0c1d510e1d5509900d56081a4c9069a\transformed\jetified-glide-4.12.0\AndroidManifest.xml:2:1-12:12
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4dd21728cb620282904f394395e0fd9e\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-auth-blockstore:16.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e42d76d94f1fcdeedaed7068e5399e7\transformed\jetified-play-services-auth-blockstore-16.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-fido:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3564b79b40c22f3f33ce1e04cdf96410\transformed\jetified-play-services-fido-21.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-identity-credentials:16.0.0-alpha02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\767b70ede55bbe0998786b69d70bb8f1\transformed\jetified-play-services-identity-credentials-16.0.0-alpha02\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b13220e95e8f412f8fdf929ca7159aa\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1330f9219eea973eec929af8fa1c5729\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-identity:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\004fa7362352e036c134d8042f2f8404\transformed\jetified-play-services-identity-18.1.0\AndroidManifest.xml:2:1-8:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b8bc891082e16b1dbfe034ba3b1a5a9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\599f7a4cda1d68eeb0a2af4979e75800\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72b780c887663d459869298f44dc09c7\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\300d6f94e6d1b4f45d88e5a7a0d3fba2\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dad1e04f1d12e58d3782d56fbc518fb\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78a0a6a50cf5903f2e10e25d8e8cd3ea\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ba767d3ac1f038378d1d0660d95028c\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86612afe8bee028d49a7fa5c79e9601e\transformed\constraintlayout-2.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.stripe:hcaptcha:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37a9eea61f7f246731189c96a915165d\transformed\jetified-hcaptcha-21.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbb5513571c3ecc71eebf495a5d90e8d\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\190cb7abb318e85f1c79a4fa923f65ed\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3cafb69718dab122b155aedd4bb28a61\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb65d20db0bdafbf79df86dc25eb4e50\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d04a35a4ee37ede57595a177ed10562\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84ebcea95efeacc174f19a164342adfc\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7706ff15a2500248677ad2acd47b5be2\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3c53645ee6d8e428687f190ceaae045\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\644cbcfa005681d6d7ac5def34be967c\transformed\jetified-lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd518ec8be27b28a26e8c21522082392\transformed\jetified-lifecycle-livedata-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ee55f53c2bafc18ef88b5208b82f5dc\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80eb008a418d0155c58fd4d4c051c66e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\760fa2757aee2315891779219e9070ae\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adc1afef20e2984fa32029d820ec40e7\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\215f629f88e644b2aa4d00c7e198828f\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7d1cf6f7265eabe26b6437df8cdcfdb\transformed\jetified-runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3124575ae01a8c110bc8e5f0b157eb92\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e882cde7a3e0600d970431e6c1b2b1a3\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a223f5d2b2514ecd1dd09366bea71d2\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.stripe:attestation:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee148be6b377a3a4a7c6c54ff4e131b\transformed\jetified-attestation-21.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.play:integrity:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd85043377d2540f00416437fb2ac7e9\transformed\jetified-integrity-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1732276c7eaabf8fe7c8d4461b851e3\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33efbd91567b410951e429bcd7c7e693\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e237f1b1c6c966f47bd0f9574b81fa0\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95d5c00a62ffa2a613f7134fa3c4f4ba\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab628adf0beae462834b52c12b84464e\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bcdbeef9428bcf2eaf588e2bb181b879\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26cc97693447936b3b8f69f2a6fa2719\transformed\fragment-1.8.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6a448fa6a634c3addcc37a4d1432544\transformed\loader-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a39702f207adeb782373e12772796a2a\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e74b3890f1d6d9c05eafca39c2cf5127\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4cc774d802693b2269c3dbae9460560\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fcf367accd9adb2a40b05c1230eee73\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f5d39c01e455345a99b57c9c4aea631\transformed\jetified-lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5ff12406a05f415564f57bbfef1a99d3\transformed\jetified-ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-viewbinding:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5153fc03d74991eab2b8e9ac53a5efa5\transformed\jetified-ui-viewbinding-1.6.8\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\885fb369f15d0f07f12fb71f12cd937d\transformed\jetified-fragment-ktx-1.8.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\814c617f1ac9ff012bf36e3770a4bced\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\321bbbd46a1a906c825850049835f2cc\transformed\jetified-activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e22591c71b40a2c4172ffe9173f59d74\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.android:facebook-bolts:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\04b9cb770066efbcdc5cffbe90480302\transformed\jetified-facebook-bolts-18.0.3\AndroidManifest.xml:9:1-17:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\490ca861c3f775c52464c68b2c18db32\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f8922862e97c4ef75dd7104d7485cc3\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\355c5ab9bc86f03453b3d02ef56e8fec\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9481e3ee1e591d121d9517f5f35bad98\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e05f257c7d66e44604f38435100db65\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92534fa39dacee9443b9933684a4e7d9\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad0a9145586392a0f19d737a934a6cb\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dce355c3308ed0f85deebc0cec080522\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d90c6124f0a7904581e279d68faa177d\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\587767e501a9ab66a3f91617d285250f\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01fdda772c29d097e47e7e07b6ab574d\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc3e2433c84cb36b07e14cfcc9de7fb0\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9b2d8195f5fa3122717d67e294e0984\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e375d717563e75bcda864b436ff0ef7e\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55768584dec4ba0be643d3fb626f8713\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db5836b85779fdc96b81a6716c0188c3\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67bfba0cf7ed9cf2e740339ee0eeb31f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1475968be37fc1cc0d45185eaabae93f\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48f5055102bd9d9e4b56e3462bbf2d4\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0238e3b1342e1795e945ca7826bc3d08\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5021680a46e39bb3bc3926e66e2ef48\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700ec4c1038357bad82e4a8518ca93ac\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\827d0526913e6e77c29cfb8917d798c3\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7af2d6d8446aedcc11b15bf921f193f3\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.databinding:viewbinding:8.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2382091193301cf94b5acbc1816adf9\transformed\jetified-viewbinding-8.8.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22c780435a20ee8d79bdb03390db482b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\815128c6d80d05f19bc4f30c4e96e4da\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c3dbe816f7c0de1f0839c6d8a557e29\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8967af0d0d410bf0eecf0cb191f0850\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad68493bc82d5a49585ae8c975c7e495\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e9138e740080f557ac80532bf22f0ff\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40787840b62239417ec0cc6c32dd9937\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\606e56decca20cee599de108723d7ece\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ac09cd70c1722cbd681cddc96971cbd\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8d47debb03072941b4414566260c2d\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78656e3a4133de4686476170d28291c1\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:2:1-15:12
MERGED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d57ee93d6de061edf1560f4205406b\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:2:1-21:12
	package
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:3:5-29
		INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:5:5-67
MERGED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:5:5-67
MERGED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:5:5-67
MERGED from [:google_sign_in_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:google_sign_in_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:7:5-67
MERGED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bda770351327ba3421df9753387b33\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bda770351327ba3421df9753387b33\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\599f7a4cda1d68eeb0a2af4979e75800\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\599f7a4cda1d68eeb0a2af4979e75800\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dad1e04f1d12e58d3782d56fbc518fb\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dad1e04f1d12e58d3782d56fbc518fb\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700ec4c1038357bad82e4a8518ca93ac\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700ec4c1038357bad82e4a8518ca93ac\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:6:5-78
MERGED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:6:5-78
MERGED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:6:5-78
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [com.stripe:stripe-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\transformed\jetified-stripe-core-21.6.0\AndroidManifest.xml:7:5-79
MERGED from [com.stripe:stripe-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\transformed\jetified-stripe-core-21.6.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bda770351327ba3421df9753387b33\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bda770351327ba3421df9753387b33\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\599f7a4cda1d68eeb0a2af4979e75800\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\599f7a4cda1d68eeb0a2af4979e75800\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dad1e04f1d12e58d3782d56fbc518fb\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dad1e04f1d12e58d3782d56fbc518fb\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700ec4c1038357bad82e4a8518ca93ac\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700ec4c1038357bad82e4a8518ca93ac\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:6:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:7:5-76
MERGED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:7:5-76
MERGED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:7:5-76
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:7:22-73
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:8:5-65
MERGED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:8:5-65
MERGED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:8:5-65
MERGED from [:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
MERGED from [:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:8:22-63
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:9:5-79
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:9:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:10:5-81
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:10:22-78
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:13:5-65
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:13:22-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:14:5-80
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:14:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:15:5-81
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:15:22-78
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:16:5-76
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:16:22-73
application
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:18:5-74:19
INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml
MERGED from [com.facebook.android:facebook-android-sdk:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b9715a8446a004a2a156cc04b2f2340\transformed\jetified-facebook-android-sdk-17.0.2\AndroidManifest.xml:16:5-17:19
MERGED from [com.facebook.android:facebook-android-sdk:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b9715a8446a004a2a156cc04b2f2340\transformed\jetified-facebook-android-sdk-17.0.2\AndroidManifest.xml:16:5-17:19
MERGED from [com.facebook.android:facebook-login:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca6706086df4d54b31c3004876d79fd4\transformed\jetified-facebook-login-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.facebook.android:facebook-login:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca6706086df4d54b31c3004876d79fd4\transformed\jetified-facebook-login-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-46:19
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-46:19
MERGED from [:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-46:19
MERGED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-46:19
MERGED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:20:5-75:19
MERGED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:20:5-75:19
MERGED from [com.facebook.android:facebook-gamingservices:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d50dddd3a008ed5d7fd91991e7962fa5\transformed\jetified-facebook-gamingservices-17.0.2\AndroidManifest.xml:16:5-17:19
MERGED from [com.facebook.android:facebook-gamingservices:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d50dddd3a008ed5d7fd91991e7962fa5\transformed\jetified-facebook-gamingservices-17.0.2\AndroidManifest.xml:16:5-17:19
MERGED from [com.facebook.android:facebook-share:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d6ffbb5596bdc2e4008a62c747de56ef\transformed\jetified-facebook-share-17.0.2\AndroidManifest.xml:16:5-17:19
MERGED from [com.facebook.android:facebook-share:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d6ffbb5596bdc2e4008a62c747de56ef\transformed\jetified-facebook-share-17.0.2\AndroidManifest.xml:16:5-17:19
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:19:5-40:19
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:19:5-40:19
MERGED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:7:5-81:19
MERGED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:7:5-81:19
MERGED from [com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a77bf49bd582bfd1c6ec842fafdd01ea\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:7:5-12:19
MERGED from [com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a77bf49bd582bfd1c6ec842fafdd01ea\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:7:5-12:19
MERGED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:14:5-71:19
MERGED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:14:5-71:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:23:5-50:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:23:5-50:19
MERGED from [com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8057826fcdfd1bf2f63ffb4797b5d13\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:7:5-12:19
MERGED from [com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8057826fcdfd1bf2f63ffb4797b5d13\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:7:5-12:19
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.facebook.android:facebook-applinks:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfd4f5a9190f985b2f0947ef3a0bf3bc\transformed\jetified-facebook-applinks-17.0.2\AndroidManifest.xml:16:5-17:19
MERGED from [com.facebook.android:facebook-applinks:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfd4f5a9190f985b2f0947ef3a0bf3bc\transformed\jetified-facebook-applinks-17.0.2\AndroidManifest.xml:16:5-17:19
MERGED from [com.facebook.android:facebook-messenger:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb00181aad37f40671409b5fc3cb8167\transformed\jetified-facebook-messenger-17.0.2\AndroidManifest.xml:16:5-17:19
MERGED from [com.facebook.android:facebook-messenger:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb00181aad37f40671409b5fc3cb8167\transformed\jetified-facebook-messenger-17.0.2\AndroidManifest.xml:16:5-17:19
MERGED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:21:5-51:19
MERGED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:21:5-51:19
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6dfcec072e49025e6fe7486ae8e78b5\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6dfcec072e49025e6fe7486ae8e78b5\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be2b43e4377e03d598e671e01a23c196\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be2b43e4377e03d598e671e01a23c196\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bda770351327ba3421df9753387b33\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bda770351327ba3421df9753387b33\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0c1d510e1d5509900d56081a4c9069a\transformed\jetified-glide-4.12.0\AndroidManifest.xml:10:5-20
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0c1d510e1d5509900d56081a4c9069a\transformed\jetified-glide-4.12.0\AndroidManifest.xml:10:5-20
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4dd21728cb620282904f394395e0fd9e\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4dd21728cb620282904f394395e0fd9e\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-auth-blockstore:16.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e42d76d94f1fcdeedaed7068e5399e7\transformed\jetified-play-services-auth-blockstore-16.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-auth-blockstore:16.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e42d76d94f1fcdeedaed7068e5399e7\transformed\jetified-play-services-auth-blockstore-16.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-fido:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3564b79b40c22f3f33ce1e04cdf96410\transformed\jetified-play-services-fido-21.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3564b79b40c22f3f33ce1e04cdf96410\transformed\jetified-play-services-fido-21.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-identity:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\004fa7362352e036c134d8042f2f8404\transformed\jetified-play-services-identity-18.1.0\AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-identity:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\004fa7362352e036c134d8042f2f8404\transformed\jetified-play-services-identity-18.1.0\AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b8bc891082e16b1dbfe034ba3b1a5a9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b8bc891082e16b1dbfe034ba3b1a5a9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\599f7a4cda1d68eeb0a2af4979e75800\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\599f7a4cda1d68eeb0a2af4979e75800\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72b780c887663d459869298f44dc09c7\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72b780c887663d459869298f44dc09c7\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\300d6f94e6d1b4f45d88e5a7a0d3fba2\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\300d6f94e6d1b4f45d88e5a7a0d3fba2\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ba767d3ac1f038378d1d0660d95028c\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ba767d3ac1f038378d1d0660d95028c\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86612afe8bee028d49a7fa5c79e9601e\transformed\constraintlayout-2.2.0\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86612afe8bee028d49a7fa5c79e9601e\transformed\constraintlayout-2.2.0\AndroidManifest.xml:7:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb65d20db0bdafbf79df86dc25eb4e50\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb65d20db0bdafbf79df86dc25eb4e50\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80eb008a418d0155c58fd4d4c051c66e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80eb008a418d0155c58fd4d4c051c66e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.play:integrity:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd85043377d2540f00416437fb2ac7e9\transformed\jetified-integrity-1.4.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd85043377d2540f00416437fb2ac7e9\transformed\jetified-integrity-1.4.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33efbd91567b410951e429bcd7c7e693\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33efbd91567b410951e429bcd7c7e693\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e237f1b1c6c966f47bd0f9574b81fa0\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e237f1b1c6c966f47bd0f9574b81fa0\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95d5c00a62ffa2a613f7134fa3c4f4ba\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95d5c00a62ffa2a613f7134fa3c4f4ba\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.facebook.android:facebook-bolts:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\04b9cb770066efbcdc5cffbe90480302\transformed\jetified-facebook-bolts-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.facebook.android:facebook-bolts:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\04b9cb770066efbcdc5cffbe90480302\transformed\jetified-facebook-bolts-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\587767e501a9ab66a3f91617d285250f\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\587767e501a9ab66a3f91617d285250f\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e375d717563e75bcda864b436ff0ef7e\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e375d717563e75bcda864b436ff0ef7e\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67bfba0cf7ed9cf2e740339ee0eeb31f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67bfba0cf7ed9cf2e740339ee0eeb31f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1475968be37fc1cc0d45185eaabae93f\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1475968be37fc1cc0d45185eaabae93f\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:9:5-20
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5021680a46e39bb3bc3926e66e2ef48\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5021680a46e39bb3bc3926e66e2ef48\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700ec4c1038357bad82e4a8518ca93ac\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700ec4c1038357bad82e4a8518ca93ac\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\606e56decca20cee599de108723d7ece\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:7:5-11:19
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\606e56decca20cee599de108723d7ece\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:7:5-11:19
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8d47debb03072941b4414566260c2d\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:11:5-20
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8d47debb03072941b4414566260c2d\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:11:5-20
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78656e3a4133de4686476170d28291c1\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:9:5-13:19
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78656e3a4133de4686476170d28291c1\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:9:5-13:19
MERGED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d57ee93d6de061edf1560f4205406b\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:11:5-19:19
MERGED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d57ee93d6de061edf1560f4205406b\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:11:5-19:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\587767e501a9ab66a3f91617d285250f\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from [com.facebook.android:facebook-android-sdk:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b9715a8446a004a2a156cc04b2f2340\transformed\jetified-facebook-android-sdk-17.0.2\AndroidManifest.xml:16:18-44
	android:label
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:20:9-38
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:22:9-42
	android:icon
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:24:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:21:9-36
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:23:9-44
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:19:9-54
activity#com.velvete.ly.MainActivity
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:25:9-43:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:31:13-49
	android:launchMode
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:27:13-43
	android:hardwareAccelerated
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:30:13-47
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:32:13-55
	android:exported
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:33:13-36
	android:configChanges
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:29:13-163
	android:theme
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:28:13-47
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:26:13-55
meta-data#io.flutter.embedding.android.NormalTheme
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:34:13-37:17
	android:resource
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:36:15-52
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:35:15-70
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:39:13-42:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:40:17-68
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:40:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:41:17-76
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:41:27-74
meta-data#flutterEmbedding
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:44:9-46:33
	android:value
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:46:13-30
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:45:13-44
meta-data#com.google.android.geo.API_KEY
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:47:9-49:71
	android:value
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:49:13-68
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:48:13-58
meta-data#com.facebook.sdk.ApplicationId
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:52:9-54:54
	android:value
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:54:13-52
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:53:13-58
meta-data#com.facebook.sdk.ClientToken
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:55:9-57:60
	android:value
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:57:13-58
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:56:13-56
activity#com.facebook.FacebookActivity
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:60:9-63:48
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:20:9-23:66
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:20:9-23:66
	android:label
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:63:13-45
	android:configChanges
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:62:13-96
	android:theme
		ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:23:13-63
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:61:13-57
activity#com.facebook.CustomTabActivity
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:64:9-73:20
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:25:9-39:20
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:25:9-39:20
	android:exported
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:66:13-36
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:65:13-58
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:@string/fb_login_protocol_scheme
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:67:13-72:29
action#android.intent.action.VIEW
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:68:17-69
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:68:25-66
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:69:17-76
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:69:27-73
category#android.intent.category.BROWSABLE
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:70:17-78
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:70:27-75
data
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:71:17-75
	android:scheme
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:71:23-72
uses-sdk
INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml
MERGED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_timezone] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_timezone\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_timezone] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_timezone\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_sign_in_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_sign_in_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\package_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\package_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:stripe_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\stripe_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:stripe_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\stripe_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.android:facebook-android-sdk:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b9715a8446a004a2a156cc04b2f2340\transformed\jetified-facebook-android-sdk-17.0.2\AndroidManifest.xml:12:5-14:41
MERGED from [com.facebook.android:facebook-android-sdk:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b9715a8446a004a2a156cc04b2f2340\transformed\jetified-facebook-android-sdk-17.0.2\AndroidManifest.xml:12:5-14:41
MERGED from [:flutter_facebook_auth] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_facebook_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_facebook_auth] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_facebook_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.android:facebook-login:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca6706086df4d54b31c3004876d79fd4\transformed\jetified-facebook-login-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-login:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ca6706086df4d54b31c3004876d79fd4\transformed\jetified-facebook-login-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_maps_flutter_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\google_maps_flutter_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_maps_flutter_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\google_maps_flutter_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_secure_storage] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_secure_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-8:53
MERGED from [:flutter_secure_storage] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_secure_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-8:53
MERGED from [:geocoding_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geocoding_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:geocoding_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geocoding_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sign_in_with_apple] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\sign_in_with_apple\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sign_in_with_apple] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\sign_in_with_apple\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acea059c377a46540689cab85dde90cd\transformed\webkit-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acea059c377a46540689cab85dde90cd\transformed\webkit-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.android:facebook-gamingservices:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d50dddd3a008ed5d7fd91991e7962fa5\transformed\jetified-facebook-gamingservices-17.0.2\AndroidManifest.xml:12:5-14:41
MERGED from [com.facebook.android:facebook-gamingservices:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d50dddd3a008ed5d7fd91991e7962fa5\transformed\jetified-facebook-gamingservices-17.0.2\AndroidManifest.xml:12:5-14:41
MERGED from [com.facebook.android:facebook-share:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d6ffbb5596bdc2e4008a62c747de56ef\transformed\jetified-facebook-share-17.0.2\AndroidManifest.xml:12:5-14:41
MERGED from [com.facebook.android:facebook-share:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d6ffbb5596bdc2e4008a62c747de56ef\transformed\jetified-facebook-share-17.0.2\AndroidManifest.xml:12:5-14:41
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:13:5-44
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:13:5-44
MERGED from [com.stripe:stripe-android:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7cd974a17593dc6d6688e8906d100e3f\transformed\jetified-stripe-android-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-android:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7cd974a17593dc6d6688e8906d100e3f\transformed\jetified-stripe-android-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a77bf49bd582bfd1c6ec842fafdd01ea\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a77bf49bd582bfd1c6ec842fafdd01ea\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b05c15e4c638bbe5f218480c9cf74bd\transformed\jetified-stripe-ui-core-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b05c15e4c638bbe5f218480c9cf74bd\transformed\jetified-stripe-ui-core-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-model:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96a01676df5203a3b615fbb3718261a\transformed\jetified-payments-model-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-model:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c96a01676df5203a3b615fbb3718261a\transformed\jetified-payments-model-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\transformed\jetified-stripe-core-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\transformed\jetified-stripe-core-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3c723f32cc31c3d5ce9263682ffa8b7\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3c723f32cc31c3d5ce9263682ffa8b7\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a3a4a579b2ef5f1ea25f0994a4694df\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a3a4a579b2ef5f1ea25f0994a4694df\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d63c9f981e85b091b6acb23f12ae70\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84d63c9f981e85b091b6acb23f12ae70\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a787c0803660539a92495da5d31ea17e\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a787c0803660539a92495da5d31ea17e\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb71e3b60d9eed6cadfc1a249b13d149\transformed\media-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb71e3b60d9eed6cadfc1a249b13d149\transformed\media-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08d12d7e609da5f0d48d54670ec0b8e6\transformed\jetified-googleid-1.1.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08d12d7e609da5f0d48d54670ec0b8e6\transformed\jetified-googleid-1.1.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3c1b17a9ffaafd0471e6527f4794f6b\transformed\jetified-credentials-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3c1b17a9ffaafd0471e6527f4794f6b\transformed\jetified-credentials-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af486666b169eb5d50216ab5d4cc9553\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af486666b169eb5d50216ab5d4cc9553\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97b0e46e5034b62169defac2cb4fe8fb\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97b0e46e5034b62169defac2cb4fe8fb\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c650d55ab1e3e720524d3af207cfc6f\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c650d55ab1e3e720524d3af207cfc6f\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c34a503dc6d29c2c14429a9f85ae2807\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c34a503dc6d29c2c14429a9f85ae2807\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc8f54f5d96ca582d8e67ebc5a19ac09\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc8f54f5d96ca582d8e67ebc5a19ac09\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d68662e109539e5ec3b632e8f6d8c967\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d68662e109539e5ec3b632e8f6d8c967\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14daecc3ebd000f4593d0172df64931b\transformed\jetified-navigation-compose-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14daecc3ebd000f4593d0172df64931b\transformed\jetified-navigation-compose-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bfe8956b361855791638c0b764997d4e\transformed\jetified-activity-compose-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bfe8956b361855791638c0b764997d4e\transformed\jetified-activity-compose-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8057826fcdfd1bf2f63ffb4797b5d13\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8057826fcdfd1bf2f63ffb4797b5d13\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\458b5494eafd4996cd399e5b49a9df4c\transformed\jetified-accompanist-systemuicontroller-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\458b5494eafd4996cd399e5b49a9df4c\transformed\jetified-accompanist-systemuicontroller-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.pay.button:compose-pay-button:0.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f46722f2ae74414dc7e075cd66b8597\transformed\jetified-compose-pay-button-0.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.pay.button:compose-pay-button:0.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f46722f2ae74414dc7e075cd66b8597\transformed\jetified-compose-pay-button-0.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-appcompat:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ae34fd4eb7d9b72497105893ff93e83\transformed\jetified-accompanist-themeadapter-appcompat-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-appcompat:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ae34fd4eb7d9b72497105893ff93e83\transformed\jetified-accompanist-themeadapter-appcompat-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-material:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01336241c67244a97fb4d45728317cf6\transformed\jetified-accompanist-themeadapter-material-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-material:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01336241c67244a97fb4d45728317cf6\transformed\jetified-accompanist-themeadapter-material-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-material3:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23dae27af0289147ae3cee436f1f9c59\transformed\jetified-accompanist-themeadapter-material3-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-material3:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23dae27af0289147ae3cee436f1f9c59\transformed\jetified-accompanist-themeadapter-material3-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.material3:material3:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb54734c218ce176bffcbd48481aca79\transformed\jetified-material3-1.0.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb54734c218ce176bffcbd48481aca79\transformed\jetified-material3-1.0.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e34cf6b06a41f14fb0ceaffd38f45e53\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e34cf6b06a41f14fb0ceaffd38f45e53\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2985daea627f0dec971a8ff5a48e644a\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2985daea627f0dec971a8ff5a48e644a\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\292fe153afec31a88f60505c2f9c1809\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\292fe153afec31a88f60505c2f9c1809\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-core:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2120daa35dbbaf918041c277632ab66b\transformed\jetified-accompanist-themeadapter-core-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-core:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2120daa35dbbaf918041c277632ab66b\transformed\jetified-accompanist-themeadapter-core-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-flowlayout:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a646fcf2c5427a40a6bfed16eecfa6e3\transformed\jetified-accompanist-flowlayout-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-flowlayout:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a646fcf2c5427a40a6bfed16eecfa6e3\transformed\jetified-accompanist-flowlayout-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25e6373e571a19f98d64d75f1f32810\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25e6373e571a19f98d64d75f1f32810\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a9760ed3571a1c2428d4c5ae6df07a4\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a9760ed3571a1c2428d4c5ae6df07a4\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30287e20ba85be6ba7bac4fc26404cb6\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30287e20ba85be6ba7bac4fc26404cb6\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\732d08034aa94dff057adb759b93ca56\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\732d08034aa94dff057adb759b93ca56\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43052af7f9775fbf90804a1b628e4dad\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43052af7f9775fbf90804a1b628e4dad\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b6c8d7cc1af8f2ca2f5d6b3f9f1ec167\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b6c8d7cc1af8f2ca2f5d6b3f9f1ec167\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16a42088a31edaba552ff082f0660291\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16a42088a31edaba552ff082f0660291\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d450bae7c21a07703659a23e148691d\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d450bae7c21a07703659a23e148691d\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32eacbaf6163f6900b1209ad53185332\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32eacbaf6163f6900b1209ad53185332\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b154d7bdb63c654ee93416929c1f009d\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b154d7bdb63c654ee93416929c1f009d\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.android:facebook-applinks:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfd4f5a9190f985b2f0947ef3a0bf3bc\transformed\jetified-facebook-applinks-17.0.2\AndroidManifest.xml:12:5-14:41
MERGED from [com.facebook.android:facebook-applinks:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfd4f5a9190f985b2f0947ef3a0bf3bc\transformed\jetified-facebook-applinks-17.0.2\AndroidManifest.xml:12:5-14:41
MERGED from [com.facebook.android:facebook-messenger:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb00181aad37f40671409b5fc3cb8167\transformed\jetified-facebook-messenger-17.0.2\AndroidManifest.xml:12:5-14:41
MERGED from [com.facebook.android:facebook-messenger:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb00181aad37f40671409b5fc3cb8167\transformed\jetified-facebook-messenger-17.0.2\AndroidManifest.xml:12:5-14:41
MERGED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6dfcec072e49025e6fe7486ae8e78b5\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6dfcec072e49025e6fe7486ae8e78b5\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be2b43e4377e03d598e671e01a23c196\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be2b43e4377e03d598e671e01a23c196\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99f04d5ed1f58b1cb06a2785f5ce6b91\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99f04d5ed1f58b1cb06a2785f5ce6b91\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5d2f88374dd98138a4945549e1432a2\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5d2f88374dd98138a4945549e1432a2\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f48494b431258a5a46659ee4e96a961\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f48494b431258a5a46659ee4e96a961\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71234f4db869dea16b06e87b8a83d62f\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71234f4db869dea16b06e87b8a83d62f\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-wallet:19.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57d1206001d3e5c2badfd9d4afb4491b\transformed\jetified-play-services-wallet-19.4.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-wallet:19.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57d1206001d3e5c2badfd9d4afb4491b\transformed\jetified-play-services-wallet-19.4.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bda770351327ba3421df9753387b33\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bda770351327ba3421df9753387b33\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0c1d510e1d5509900d56081a4c9069a\transformed\jetified-glide-4.12.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0c1d510e1d5509900d56081a4c9069a\transformed\jetified-glide-4.12.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4dd21728cb620282904f394395e0fd9e\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4dd21728cb620282904f394395e0fd9e\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-blockstore:16.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e42d76d94f1fcdeedaed7068e5399e7\transformed\jetified-play-services-auth-blockstore-16.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-blockstore:16.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e42d76d94f1fcdeedaed7068e5399e7\transformed\jetified-play-services-auth-blockstore-16.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3564b79b40c22f3f33ce1e04cdf96410\transformed\jetified-play-services-fido-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3564b79b40c22f3f33ce1e04cdf96410\transformed\jetified-play-services-fido-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-identity-credentials:16.0.0-alpha02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\767b70ede55bbe0998786b69d70bb8f1\transformed\jetified-play-services-identity-credentials-16.0.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-identity-credentials:16.0.0-alpha02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\767b70ede55bbe0998786b69d70bb8f1\transformed\jetified-play-services-identity-credentials-16.0.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b13220e95e8f412f8fdf929ca7159aa\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b13220e95e8f412f8fdf929ca7159aa\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1330f9219eea973eec929af8fa1c5729\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1330f9219eea973eec929af8fa1c5729\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-identity:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\004fa7362352e036c134d8042f2f8404\transformed\jetified-play-services-identity-18.1.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-identity:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\004fa7362352e036c134d8042f2f8404\transformed\jetified-play-services-identity-18.1.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b8bc891082e16b1dbfe034ba3b1a5a9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b8bc891082e16b1dbfe034ba3b1a5a9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\599f7a4cda1d68eeb0a2af4979e75800\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\599f7a4cda1d68eeb0a2af4979e75800\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72b780c887663d459869298f44dc09c7\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72b780c887663d459869298f44dc09c7\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\300d6f94e6d1b4f45d88e5a7a0d3fba2\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\300d6f94e6d1b4f45d88e5a7a0d3fba2\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dad1e04f1d12e58d3782d56fbc518fb\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dad1e04f1d12e58d3782d56fbc518fb\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78a0a6a50cf5903f2e10e25d8e8cd3ea\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78a0a6a50cf5903f2e10e25d8e8cd3ea\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ba767d3ac1f038378d1d0660d95028c\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ba767d3ac1f038378d1d0660d95028c\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86612afe8bee028d49a7fa5c79e9601e\transformed\constraintlayout-2.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86612afe8bee028d49a7fa5c79e9601e\transformed\constraintlayout-2.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:hcaptcha:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37a9eea61f7f246731189c96a915165d\transformed\jetified-hcaptcha-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:hcaptcha:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37a9eea61f7f246731189c96a915165d\transformed\jetified-hcaptcha-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbb5513571c3ecc71eebf495a5d90e8d\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbb5513571c3ecc71eebf495a5d90e8d\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\190cb7abb318e85f1c79a4fa923f65ed\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\190cb7abb318e85f1c79a4fa923f65ed\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3cafb69718dab122b155aedd4bb28a61\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3cafb69718dab122b155aedd4bb28a61\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb65d20db0bdafbf79df86dc25eb4e50\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb65d20db0bdafbf79df86dc25eb4e50\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d04a35a4ee37ede57595a177ed10562\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d04a35a4ee37ede57595a177ed10562\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84ebcea95efeacc174f19a164342adfc\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84ebcea95efeacc174f19a164342adfc\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7706ff15a2500248677ad2acd47b5be2\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7706ff15a2500248677ad2acd47b5be2\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3c53645ee6d8e428687f190ceaae045\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e3c53645ee6d8e428687f190ceaae045\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\644cbcfa005681d6d7ac5def34be967c\transformed\jetified-lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\644cbcfa005681d6d7ac5def34be967c\transformed\jetified-lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd518ec8be27b28a26e8c21522082392\transformed\jetified-lifecycle-livedata-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd518ec8be27b28a26e8c21522082392\transformed\jetified-lifecycle-livedata-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ee55f53c2bafc18ef88b5208b82f5dc\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ee55f53c2bafc18ef88b5208b82f5dc\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80eb008a418d0155c58fd4d4c051c66e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80eb008a418d0155c58fd4d4c051c66e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\760fa2757aee2315891779219e9070ae\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\760fa2757aee2315891779219e9070ae\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adc1afef20e2984fa32029d820ec40e7\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adc1afef20e2984fa32029d820ec40e7\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\215f629f88e644b2aa4d00c7e198828f\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\215f629f88e644b2aa4d00c7e198828f\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7d1cf6f7265eabe26b6437df8cdcfdb\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7d1cf6f7265eabe26b6437df8cdcfdb\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3124575ae01a8c110bc8e5f0b157eb92\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3124575ae01a8c110bc8e5f0b157eb92\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e882cde7a3e0600d970431e6c1b2b1a3\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e882cde7a3e0600d970431e6c1b2b1a3\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a223f5d2b2514ecd1dd09366bea71d2\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a223f5d2b2514ecd1dd09366bea71d2\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [com.stripe:attestation:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee148be6b377a3a4a7c6c54ff4e131b\transformed\jetified-attestation-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:attestation:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ee148be6b377a3a4a7c6c54ff4e131b\transformed\jetified-attestation-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd85043377d2540f00416437fb2ac7e9\transformed\jetified-integrity-1.4.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd85043377d2540f00416437fb2ac7e9\transformed\jetified-integrity-1.4.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1732276c7eaabf8fe7c8d4461b851e3\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1732276c7eaabf8fe7c8d4461b851e3\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33efbd91567b410951e429bcd7c7e693\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33efbd91567b410951e429bcd7c7e693\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e237f1b1c6c966f47bd0f9574b81fa0\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e237f1b1c6c966f47bd0f9574b81fa0\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95d5c00a62ffa2a613f7134fa3c4f4ba\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95d5c00a62ffa2a613f7134fa3c4f4ba\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab628adf0beae462834b52c12b84464e\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab628adf0beae462834b52c12b84464e\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bcdbeef9428bcf2eaf588e2bb181b879\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bcdbeef9428bcf2eaf588e2bb181b879\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26cc97693447936b3b8f69f2a6fa2719\transformed\fragment-1.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26cc97693447936b3b8f69f2a6fa2719\transformed\fragment-1.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6a448fa6a634c3addcc37a4d1432544\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6a448fa6a634c3addcc37a4d1432544\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a39702f207adeb782373e12772796a2a\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a39702f207adeb782373e12772796a2a\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e74b3890f1d6d9c05eafca39c2cf5127\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e74b3890f1d6d9c05eafca39c2cf5127\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4cc774d802693b2269c3dbae9460560\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4cc774d802693b2269c3dbae9460560\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fcf367accd9adb2a40b05c1230eee73\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fcf367accd9adb2a40b05c1230eee73\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f5d39c01e455345a99b57c9c4aea631\transformed\jetified-lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f5d39c01e455345a99b57c9c4aea631\transformed\jetified-lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5ff12406a05f415564f57bbfef1a99d3\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5ff12406a05f415564f57bbfef1a99d3\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-viewbinding:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5153fc03d74991eab2b8e9ac53a5efa5\transformed\jetified-ui-viewbinding-1.6.8\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-viewbinding:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5153fc03d74991eab2b8e9ac53a5efa5\transformed\jetified-ui-viewbinding-1.6.8\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\885fb369f15d0f07f12fb71f12cd937d\transformed\jetified-fragment-ktx-1.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\885fb369f15d0f07f12fb71f12cd937d\transformed\jetified-fragment-ktx-1.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\814c617f1ac9ff012bf36e3770a4bced\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\814c617f1ac9ff012bf36e3770a4bced\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\321bbbd46a1a906c825850049835f2cc\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\321bbbd46a1a906c825850049835f2cc\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e22591c71b40a2c4172ffe9173f59d74\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e22591c71b40a2c4172ffe9173f59d74\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.android:facebook-bolts:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\04b9cb770066efbcdc5cffbe90480302\transformed\jetified-facebook-bolts-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-bolts:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\04b9cb770066efbcdc5cffbe90480302\transformed\jetified-facebook-bolts-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\490ca861c3f775c52464c68b2c18db32\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\490ca861c3f775c52464c68b2c18db32\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f8922862e97c4ef75dd7104d7485cc3\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f8922862e97c4ef75dd7104d7485cc3\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\355c5ab9bc86f03453b3d02ef56e8fec\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\355c5ab9bc86f03453b3d02ef56e8fec\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9481e3ee1e591d121d9517f5f35bad98\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9481e3ee1e591d121d9517f5f35bad98\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e05f257c7d66e44604f38435100db65\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e05f257c7d66e44604f38435100db65\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92534fa39dacee9443b9933684a4e7d9\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92534fa39dacee9443b9933684a4e7d9\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad0a9145586392a0f19d737a934a6cb\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ad0a9145586392a0f19d737a934a6cb\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dce355c3308ed0f85deebc0cec080522\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dce355c3308ed0f85deebc0cec080522\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d90c6124f0a7904581e279d68faa177d\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d90c6124f0a7904581e279d68faa177d\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\587767e501a9ab66a3f91617d285250f\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\587767e501a9ab66a3f91617d285250f\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01fdda772c29d097e47e7e07b6ab574d\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01fdda772c29d097e47e7e07b6ab574d\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc3e2433c84cb36b07e14cfcc9de7fb0\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc3e2433c84cb36b07e14cfcc9de7fb0\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9b2d8195f5fa3122717d67e294e0984\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9b2d8195f5fa3122717d67e294e0984\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e375d717563e75bcda864b436ff0ef7e\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e375d717563e75bcda864b436ff0ef7e\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55768584dec4ba0be643d3fb626f8713\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55768584dec4ba0be643d3fb626f8713\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db5836b85779fdc96b81a6716c0188c3\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db5836b85779fdc96b81a6716c0188c3\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67bfba0cf7ed9cf2e740339ee0eeb31f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67bfba0cf7ed9cf2e740339ee0eeb31f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1475968be37fc1cc0d45185eaabae93f\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1475968be37fc1cc0d45185eaabae93f\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48f5055102bd9d9e4b56e3462bbf2d4\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c48f5055102bd9d9e4b56e3462bbf2d4\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0238e3b1342e1795e945ca7826bc3d08\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0238e3b1342e1795e945ca7826bc3d08\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5021680a46e39bb3bc3926e66e2ef48\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5021680a46e39bb3bc3926e66e2ef48\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700ec4c1038357bad82e4a8518ca93ac\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700ec4c1038357bad82e4a8518ca93ac\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\827d0526913e6e77c29cfb8917d798c3\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\827d0526913e6e77c29cfb8917d798c3\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7af2d6d8446aedcc11b15bf921f193f3\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7af2d6d8446aedcc11b15bf921f193f3\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.databinding:viewbinding:8.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2382091193301cf94b5acbc1816adf9\transformed\jetified-viewbinding-8.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d2382091193301cf94b5acbc1816adf9\transformed\jetified-viewbinding-8.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22c780435a20ee8d79bdb03390db482b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22c780435a20ee8d79bdb03390db482b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\815128c6d80d05f19bc4f30c4e96e4da\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\815128c6d80d05f19bc4f30c4e96e4da\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c3dbe816f7c0de1f0839c6d8a557e29\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c3dbe816f7c0de1f0839c6d8a557e29\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8967af0d0d410bf0eecf0cb191f0850\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8967af0d0d410bf0eecf0cb191f0850\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad68493bc82d5a49585ae8c975c7e495\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad68493bc82d5a49585ae8c975c7e495\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e9138e740080f557ac80532bf22f0ff\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e9138e740080f557ac80532bf22f0ff\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40787840b62239417ec0cc6c32dd9937\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40787840b62239417ec0cc6c32dd9937\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\606e56decca20cee599de108723d7ece\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\606e56decca20cee599de108723d7ece\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ac09cd70c1722cbd681cddc96971cbd\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ac09cd70c1722cbd681cddc96971cbd\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8d47debb03072941b4414566260c2d\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8d47debb03072941b4414566260c2d\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78656e3a4133de4686476170d28291c1\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78656e3a4133de4686476170d28291c1\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d57ee93d6de061edf1560f4205406b\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d57ee93d6de061edf1560f4205406b\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [:flutter_secure_storage] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_secure_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-50
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml
uses-permission#com.sec.android.provider.badge.permission.READ
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-86
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-83
uses-permission#com.sec.android.provider.badge.permission.WRITE
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-87
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-84
uses-permission#com.htc.launcher.permission.READ_SETTINGS
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-81
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:22-78
uses-permission#com.htc.launcher.permission.UPDATE_SHORTCUT
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-83
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:22-80
uses-permission#com.sonyericsson.home.permission.BROADCAST_BADGE
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:5-88
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:22-85
uses-permission#com.sonymobile.home.permission.PROVIDER_INSERT_BADGE
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:5-92
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:22-89
uses-permission#com.anddoes.launcher.permission.UPDATE_COUNT
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:5-84
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:22-81
uses-permission#com.majeur.launcher.permission.UPDATE_BADGE
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:5-83
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:22-80
uses-permission#com.huawei.android.launcher.permission.CHANGE_BADGE
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:5-91
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:22-88
uses-permission#com.huawei.android.launcher.permission.READ_SETTINGS
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:5-92
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:22-89
uses-permission#com.huawei.android.launcher.permission.WRITE_SETTINGS
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:5-93
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:22-90
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-77
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:23:5-77
	android:name
		ADDED from [:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
service#com.baseflow.geolocator.GeolocatorLocationService
ADDED from [:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
	android:enabled
		ADDED from [:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
	android:exported
		ADDED from [:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
	android:foregroundServiceType
		ADDED from [:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
	android:name
		ADDED from [:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
	android:resource
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
	android:enabled
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
	tools:ignore
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-40
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
meta-data#photopicker_activity:0:required
ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
	android:value
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
uses-permission#android.permission.WAKE_LOCK
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dad1e04f1d12e58d3782d56fbc518fb\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dad1e04f1d12e58d3782d56fbc518fb\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
service#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
	android:exported
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
	android:permission
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
service#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-24:19
	android:exported
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-97
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
receiver#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
	android:exported
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
	android:permission
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-39:19
MERGED from [:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\599f7a4cda1d68eeb0a2af4979e75800\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\599f7a4cda1d68eeb0a2af4979e75800\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72b780c887663d459869298f44dc09c7\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72b780c887663d459869298f44dc09c7\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5021680a46e39bb3bc3926e66e2ef48\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5021680a46e39bb3bc3926e66e2ef48\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:18-89
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
	android:value
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
provider#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
	android:authorities
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
	android:exported
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
	android:initOrder
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
ADDED from [:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
queries
ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:15
MERGED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:10:5-18:15
MERGED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:10:5-18:15
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:15:5-17:15
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:15:5-17:15
MERGED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:8:5-12:15
MERGED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:8:5-12:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bda770351327ba3421df9753387b33\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bda770351327ba3421df9753387b33\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-90
	android:name
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-87
activity#com.pichillilorenzo.flutter_inappwebview_android.in_app_browser.InAppBrowserActivity
ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-18:47
	android:exported
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
	android:configChanges
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-137
	android:theme
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-44
	android:name
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-112
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivity
ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-22:55
	android:exported
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-120
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivity
ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-26:55
	android:exported
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-114
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivitySingleInstance
ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:9-31:55
	android:launchMode
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-48
	android:exported
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-134
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivitySingleInstance
ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-36:55
	android:launchMode
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-48
	android:exported
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-128
receiver#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ActionBroadcastReceiver
ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:9-41:40
	android:enabled
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-35
	android:exported
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-119
meta-data#io.flutter.embedded_views_preview
ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:9-45:36
	android:value
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-33
	android:name
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-61
intent#action:name:android.intent.action.VIEW+data:scheme:http
ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:13:9-17:18
activity#com.stripe.android.financialconnections.FinancialConnectionsSheetRedirectActivity
ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:21:9-65:20
	android:launchMode
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:24:13-44
	android:exported
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:23:13-36
	android:name
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:22:13-109
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:auth-redirect+data:host:link-accounts+data:host:link-accounts+data:host:link-accounts+data:host:link-native-accounts+data:host:native-redirect+data:path:/${applicationId}/cancel+data:path:/${applicationId}/success+data:pathPrefix:/${applicationId}+data:pathPrefix:/${applicationId}+data:pathPrefix:/${applicationId}/authentication_return+data:pathPrefix:/${applicationId}/authentication_return+data:scheme:stripe+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth
ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:25:13-64:29
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:auth-redirect+data:host:link-accounts+data:host:link-accounts+data:host:link-accounts+data:host:link-native-accounts+data:host:native-redirect+data:path:/com.velvete.ly/cancel+data:path:/com.velvete.ly/success+data:pathPrefix:/com.velvete.ly+data:pathPrefix:/com.velvete.ly+data:pathPrefix:/com.velvete.ly/authentication_return+data:pathPrefix:/com.velvete.ly/authentication_return+data:scheme:stripe+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth
ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:25:13-64:29
activity#com.stripe.android.financialconnections.FinancialConnectionsSheetActivity
ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:66:9-69:77
	android:exported
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:68:13-37
	android:theme
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:69:13-74
	android:name
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:67:13-101
activity#com.stripe.android.financialconnections.ui.FinancialConnectionsSheetNativeActivity
ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:70:9-74:58
	android:windowSoftInputMode
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:74:13-55
	android:exported
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:72:13-37
	android:theme
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:73:13-74
	android:name
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:71:13-110
package#com.facebook.katana
ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:16:9-55
	android:name
		ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:16:18-52
activity#com.facebook.CustomTabMainActivity
ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:24:9-71
	android:name
		ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:24:19-68
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:cct.${applicationId}+data:scheme:fbconnect
ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:29:13-38:29
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:cct.com.velvete.ly+data:scheme:fbconnect
ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:29:13-38:29
activity#com.stripe.android.paymentsheet.PaymentSheetActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:8:9-11:69
	android:exported
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:11:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:9:13-80
activity#com.stripe.android.paymentsheet.PaymentOptionsActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:12:9-15:69
	android:exported
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:14:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:15:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:13:13-82
activity#com.stripe.android.customersheet.CustomerSheetActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:16:9-19:69
	android:exported
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:18:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:19:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:17:13-82
activity#com.stripe.android.paymentsheet.addresselement.AddressElementActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:20:9-23:69
	android:exported
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:22:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:21:13-97
activity#com.stripe.android.paymentsheet.paymentdatacollection.bacs.BacsMandateConfirmationActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:24:9-27:69
	android:exported
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:27:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:25:13-118
activity#com.stripe.android.paymentsheet.paymentdatacollection.polling.PollingActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:28:9-31:69
	android:exported
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:30:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:31:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:29:13-105
activity#com.stripe.android.paymentsheet.ui.SepaMandateActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:32:9-35:69
	android:exported
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:34:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:35:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:33:13-82
activity#com.stripe.android.paymentsheet.ExternalPaymentMethodProxyActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:36:9-39:68
	android:exported
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:38:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:39:13-65
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:37:13-94
activity#com.stripe.android.paymentsheet.paymentdatacollection.cvcrecollection.CvcRecollectionActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:40:9-42:69
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:42:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:41:13-121
activity#com.stripe.android.paymentelement.embedded.form.FormActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:43:9-45:69
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:45:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:44:13-88
activity#com.stripe.android.paymentelement.embedded.manage.ManageActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:46:9-48:69
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:48:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:47:13-92
activity#com.stripe.android.link.LinkActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:49:9-56:58
	android:label
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:54:13-48
	android:autoRemoveFromRecents
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:51:13-49
	android:windowSoftInputMode
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:56:13-55
	android:exported
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:53:13-37
	android:configChanges
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:52:13-115
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:55:13-55
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:50:13-64
activity#com.stripe.android.link.LinkForegroundActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:57:9-62:61
	android:launchMode
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:61:13-43
	android:autoRemoveFromRecents
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:59:13-49
	android:configChanges
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:60:13-115
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:62:13-58
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:58:13-74
activity#com.stripe.android.link.LinkRedirectHandlerActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:63:9-80:20
	android:launchMode
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:67:13-48
	android:autoRemoveFromRecents
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:65:13-49
	android:exported
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:66:13-36
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:68:13-58
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:64:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:complete+data:path:/${applicationId}+data:scheme:link-popup
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:69:13-79:29
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:complete+data:path:/com.velvete.ly+data:scheme:link-popup
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:69:13-79:29
activity#com.stripe.android.ui.core.cardscan.CardScanActivity
ADDED from [com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a77bf49bd582bfd1c6ec842fafdd01ea\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:8:9-11:69
	android:exported
		ADDED from [com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a77bf49bd582bfd1c6ec842fafdd01ea\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a77bf49bd582bfd1c6ec842fafdd01ea\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:11:13-66
	android:name
		ADDED from [com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a77bf49bd582bfd1c6ec842fafdd01ea\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:9:13-80
package#com.android.chrome
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:11:9-54
	android:name
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:11:18-51
activity#com.stripe.android.view.PaymentAuthWebViewActivity
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:15:9-18:57
	android:exported
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:17:13-37
	android:theme
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:18:13-54
	android:name
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:16:13-78
activity#com.stripe.android.view.PaymentRelayActivity
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:19:9-22:61
	android:exported
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:21:13-37
	android:theme
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:22:13-58
	android:name
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:20:13-72
activity#com.stripe.android.payments.StripeBrowserLauncherActivity
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:28:9-32:61
	android:launchMode
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:31:13-44
	android:exported
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:30:13-37
	android:theme
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:32:13-58
	android:name
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:29:13-85
activity#com.stripe.android.payments.StripeBrowserProxyReturnActivity
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:33:9-50:20
	android:launchMode
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:36:13-44
	android:exported
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:35:13-36
	android:theme
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:37:13-58
	android:name
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:34:13-88
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:payment_return_url+data:path:/${applicationId}+data:scheme:stripesdk
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:38:13-49:29
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:payment_return_url+data:path:/com.velvete.ly+data:scheme:stripesdk
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:38:13-49:29
activity#com.stripe.android.payments.core.authentication.threeds2.Stripe3ds2TransactionActivity
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:51:9-54:57
	android:exported
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:53:13-37
	android:theme
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:54:13-54
	android:name
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:52:13-114
activity#com.stripe.android.googlepaylauncher.GooglePayLauncherActivity
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:55:9-58:66
	android:exported
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:57:13-37
	android:theme
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:58:13-63
	android:name
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:56:13-90
activity#com.stripe.android.googlepaylauncher.GooglePayPaymentMethodLauncherActivity
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:59:9-62:66
	android:exported
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:61:13-37
	android:theme
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:62:13-63
	android:name
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:60:13-103
activity#com.stripe.android.payments.paymentlauncher.PaymentLauncherConfirmationActivity
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:63:9-66:68
	android:exported
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:65:13-37
	android:theme
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:66:13-65
	android:name
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:64:13-107
activity#com.stripe.android.payments.bankaccount.ui.CollectBankAccountActivity
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:67:9-70:61
	android:exported
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:69:13-37
	android:theme
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:70:13-58
	android:name
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:68:13-97
service#androidx.credentials.playservices.CredentialProviderMetadataHolder
ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:24:9-32:19
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:26:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:28:13-60
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:25:13-94
meta-data#androidx.credentials.CREDENTIAL_PROVIDER_KEY
ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:29:13-31:104
	android:value
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:31:17-101
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:30:17-76
activity#androidx.credentials.playservices.HiddenActivity
ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:34:9-41:20
	android:fitsSystemWindows
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:39:13-45
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:38:13-37
	android:configChanges
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:36:13-87
	android:theme
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:40:13-48
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:35:13-76
activity#androidx.credentials.playservices.IdentityCredentialApiHiddenActivity
ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:42:9-49:20
	android:fitsSystemWindows
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:47:13-45
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:45:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:46:13-37
	android:configChanges
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:44:13-87
	android:theme
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:48:13-48
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:43:13-97
uses-permission#android.permission.USE_BIOMETRIC
ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af486666b169eb5d50216ab5d4cc9553\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
	android:name
		ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af486666b169eb5d50216ab5d4cc9553\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
uses-permission#android.permission.USE_FINGERPRINT
ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af486666b169eb5d50216ab5d4cc9553\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
	android:name
		ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af486666b169eb5d50216ab5d4cc9553\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
activity#com.stripe.android.stripe3ds2.views.ChallengeActivity
ADDED from [com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8057826fcdfd1bf2f63ffb4797b5d13\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:8:9-11:54
	android:exported
		ADDED from [com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8057826fcdfd1bf2f63ffb4797b5d13\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8057826fcdfd1bf2f63ffb4797b5d13\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:11:13-51
	android:name
		ADDED from [com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8057826fcdfd1bf2f63ffb4797b5d13\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:9:13-81
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:14:5-79
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:14:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:16:5-88
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:16:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:17:5-82
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:17:22-79
uses-permission#android.permission.ACCESS_ADSERVICES_CUSTOM_AUDIENCE
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:18:5-92
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:18:22-89
uses-permission#android.permission.ACCESS_ADSERVICES_TOPICS
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:19:5-83
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:19:22-80
provider#com.facebook.internal.FacebookInitProvider
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:32:9-35:40
	android:authorities
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:34:13-72
	android:exported
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:35:13-37
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:33:13-70
receiver#com.facebook.CurrentAccessTokenExpirationBroadcastReceiver
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:37:9-43:20
	android:exported
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:39:13-37
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:38:13-86
intent-filter#action:name:com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:40:13-42:29
action#com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:41:17-95
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:41:25-92
receiver#com.facebook.AuthenticationTokenManager$CurrentAuthenticationTokenChangedBroadcastReceiver
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:44:9-50:20
	android:exported
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:46:13-37
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:45:13-118
intent-filter#action:name:com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:47:13-49:29
action#com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:48:17-103
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:48:25-100
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dad1e04f1d12e58d3782d56fbc518fb\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dad1e04f1d12e58d3782d56fbc518fb\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:30:13-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:47:13-82
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:61:17-119
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bda770351327ba3421df9753387b33\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bda770351327ba3421df9753387b33\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bda770351327ba3421df9753387b33\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
package#com.google.android.apps.maps
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bda770351327ba3421df9753387b33\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bda770351327ba3421df9753387b33\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bda770351327ba3421df9753387b33\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bda770351327ba3421df9753387b33\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bda770351327ba3421df9753387b33\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b8bc891082e16b1dbfe034ba3b1a5a9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b8bc891082e16b1dbfe034ba3b1a5a9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b8bc891082e16b1dbfe034ba3b1a5a9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b8bc891082e16b1dbfe034ba3b1a5a9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\599f7a4cda1d68eeb0a2af4979e75800\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\599f7a4cda1d68eeb0a2af4979e75800\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\599f7a4cda1d68eeb0a2af4979e75800\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\599f7a4cda1d68eeb0a2af4979e75800\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\599f7a4cda1d68eeb0a2af4979e75800\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\599f7a4cda1d68eeb0a2af4979e75800\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72b780c887663d459869298f44dc09c7\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72b780c887663d459869298f44dc09c7\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72b780c887663d459869298f44dc09c7\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ba767d3ac1f038378d1d0660d95028c\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ba767d3ac1f038378d1d0660d95028c\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ba767d3ac1f038378d1d0660d95028c\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ba767d3ac1f038378d1d0660d95028c\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ba767d3ac1f038378d1d0660d95028c\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ba767d3ac1f038378d1d0660d95028c\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb65d20db0bdafbf79df86dc25eb4e50\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80eb008a418d0155c58fd4d4c051c66e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80eb008a418d0155c58fd4d4c051c66e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e375d717563e75bcda864b436ff0ef7e\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e375d717563e75bcda864b436ff0ef7e\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb65d20db0bdafbf79df86dc25eb4e50\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb65d20db0bdafbf79df86dc25eb4e50\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb65d20db0bdafbf79df86dc25eb4e50\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb65d20db0bdafbf79df86dc25eb4e50\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb65d20db0bdafbf79df86dc25eb4e50\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb65d20db0bdafbf79df86dc25eb4e50\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb65d20db0bdafbf79df86dc25eb4e50\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80eb008a418d0155c58fd4d4c051c66e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80eb008a418d0155c58fd4d4c051c66e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80eb008a418d0155c58fd4d4c051c66e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95d5c00a62ffa2a613f7134fa3c4f4ba\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\606e56decca20cee599de108723d7ece\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:8:9-10:69
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\606e56decca20cee599de108723d7ece\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:8:9-10:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95d5c00a62ffa2a613f7134fa3c4f4ba\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95d5c00a62ffa2a613f7134fa3c4f4ba\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\587767e501a9ab66a3f91617d285250f\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\587767e501a9ab66a3f91617d285250f\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\587767e501a9ab66a3f91617d285250f\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.velvete.ly.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\587767e501a9ab66a3f91617d285250f\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\587767e501a9ab66a3f91617d285250f\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\587767e501a9ab66a3f91617d285250f\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\587767e501a9ab66a3f91617d285250f\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\587767e501a9ab66a3f91617d285250f\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.velvete.ly.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\587767e501a9ab66a3f91617d285250f\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\587767e501a9ab66a3f91617d285250f\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5021680a46e39bb3bc3926e66e2ef48\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5021680a46e39bb3bc3926e66e2ef48\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5021680a46e39bb3bc3926e66e2ef48\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700ec4c1038357bad82e4a8518ca93ac\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700ec4c1038357bad82e4a8518ca93ac\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700ec4c1038357bad82e4a8518ca93ac\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700ec4c1038357bad82e4a8518ca93ac\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700ec4c1038357bad82e4a8518ca93ac\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700ec4c1038357bad82e4a8518ca93ac\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8d47debb03072941b4414566260c2d\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:5-110
	android:name
		ADDED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8d47debb03072941b4414566260c2d\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:22-107
meta-data#aia-compat-api-min-version
ADDED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78656e3a4133de4686476170d28291c1\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:10:9-12:33
	android:value
		ADDED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78656e3a4133de4686476170d28291c1\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:12:13-30
	android:name
		ADDED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78656e3a4133de4686476170d28291c1\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:11:13-54
activity#com.google.android.play.core.common.PlayCoreDialogWrapperActivity
ADDED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d57ee93d6de061edf1560f4205406b\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:14:9-18:65
	android:stateNotNeeded
		ADDED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d57ee93d6de061edf1560f4205406b\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:17:13-42
	android:exported
		ADDED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d57ee93d6de061edf1560f4205406b\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:16:13-37
	android:theme
		ADDED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d57ee93d6de061edf1560f4205406b\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:18:13-62
	android:name
		ADDED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d57ee93d6de061edf1560f4205406b\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:15:13-93
