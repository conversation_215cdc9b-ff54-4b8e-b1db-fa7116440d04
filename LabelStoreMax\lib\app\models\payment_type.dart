//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import '/app/providers/payments/cash_on_delivery.dart';

class PaymentType {
  int id;
  String name;
  String desc;
  String assetImage;
  Function pay;
  String? gatewayId; // WooCommerce gateway ID
  String? description; // Full WooCommerce description
  Map<String, dynamic>? settings; // Gateway settings

  PaymentType({
    required this.id,
    required this.name,
    required this.desc,
    required this.assetImage,
    required this.pay,
    this.gatewayId,
    this.description,
    this.settings,
  });

  /// Create PaymentType from WooCommerce gateway data
  factory PaymentType.fromWooGateway(Map<String, dynamic> gateway) {
    return PaymentType(
      id: gateway['id'].hashCode, // Generate numeric ID from string ID
      name: gateway['title'] ?? gateway['method_title'] ?? 'Unknown Payment',
      desc: gateway['description'] ?? gateway['method_description'] ?? '',
      assetImage: _getAssetImageForGateway(gateway['id']),
      pay: _getPaymentFunction(gateway['id']),
      gatewayId: gateway['id'],
      description: gateway['description'],
      settings: gateway['settings'],
    );
  }

  /// Get appropriate asset image based on gateway ID
  static String _getAssetImageForGateway(String gatewayId) {
    switch (gatewayId.toLowerCase()) {
      case 'cod':
        return 'public/images/payment_page/psyment page illutration.png'; // Fixed: Use existing payment illustration
      case 'bacs':
        return 'public/images/credit_cards.png'; // Fixed: Use existing credit cards image
      case 'cheque':
        return 'public/images/credit_cards.png'; // Fixed: Use existing credit cards image
      case 'paypal':
        return 'public/images/credit_cards.png'; // Fixed: Use existing credit cards image
      case 'stripe':
        return 'public/images/credit_cards.png'; // Fixed: Use existing credit cards image
      default:
        return 'public/images/credit_cards.png'; // Fixed: Use existing credit cards image
    }
  }

  /// Get appropriate payment function based on gateway ID
  static Function _getPaymentFunction(String gatewayId) {
    switch (gatewayId.toLowerCase()) {
      case 'cod':
        return _cashOnDeliveryPay;
      case 'bacs':
        return _bankTransferPay;
      default:
        return _defaultPay;
    }
  }

  /// Cash on Delivery payment function
  static Future<void> _cashOnDeliveryPay(context) async {
    // Use the actual cash on delivery implementation
    await cashOnDeliveryPay(context);
  }

  /// Bank Transfer payment function
  static Future<void> _bankTransferPay(context) async {
    // For bank transfer, use the same cash on delivery flow but mark as unpaid
    await cashOnDeliveryPay(context);
  }

  /// Default payment function
  static Future<void> _defaultPay(context) async {
    // Default to cash on delivery for any unrecognized payment method
    await cashOnDeliveryPay(context);
  }
}
