//  Velvete Store
//
//  Created by <PERSON><PERSON>.
//  2025, <PERSON><PERSON>. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class CachedImageWidget extends StatelessWidget {
  const CachedImageWidget({
    super.key,
    this.image,
    this.height = 70,
    this.width = 70,
    this.placeholder,
    this.fit = BoxFit.contain,
  });

  final String? image;
  final double height;
  final double width;
  final Widget? placeholder;
  final BoxFit fit;

  @override
  Widget build(BuildContext context) {
    // Handle null or empty image URLs
    if (image == null || image!.isEmpty) {
      return LayoutBuilder(
        builder: (context, constraints) {
          final safeHeight = height.isFinite ? height : constraints.maxHeight.isFinite ? constraints.maxHeight : 70.0;
          final iconSize = (safeHeight * 0.3).clamp(16.0, 48.0);

          return Container(
            height: height.isFinite ? height : null,
            width: width.isFinite ? width : null,
            alignment: Alignment.center,
            child: Icon(
              Icons.image_not_supported,
              size: iconSize,
              color: Theme.of(context).colorScheme.outline,
            ),
          );
        },
      );
    }

    return CachedNetworkImage(
      imageUrl: image!,
      placeholder: (context, url) => placeholder ?? Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
          backgroundColor: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
        ),
      ),
      errorWidget: (context, url, error) => LayoutBuilder(
        builder: (context, constraints) {
          final safeHeight = height.isFinite ? height : constraints.maxHeight.isFinite ? constraints.maxHeight : 70.0;
          final iconSize = (safeHeight * 0.3).clamp(16.0, 48.0);

          return Container(
            height: height.isFinite ? height : null,
            width: width.isFinite ? width : null,
            alignment: Alignment.center,
            child: Icon(
              Icons.broken_image,
              size: iconSize,
              color: Theme.of(context).colorScheme.outline,
            ),
          );
        },
      ),
      height: height.isFinite ? height : null,
      width: width.isFinite ? width : null,
      alignment: Alignment.center,
      fit: fit,
      // Enhanced performance optimizations
      memCacheHeight: (height.isFinite && height > 200) ? 600 : 300,
      memCacheWidth: (width.isFinite && width > 200) ? 600 : 300,
      maxHeightDiskCache: 800,
      maxWidthDiskCache: 800,
      // Add fade animation for smoother loading
      fadeInDuration: Duration(milliseconds: 200),
      fadeOutDuration: Duration(milliseconds: 100),
    );
  }
}
