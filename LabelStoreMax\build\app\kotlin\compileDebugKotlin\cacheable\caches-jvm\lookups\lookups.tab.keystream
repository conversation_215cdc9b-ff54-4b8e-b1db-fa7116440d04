  Application android.app  GeneratedPluginRegistrant android.app.Activity  println android.app.Activity  AppEventsLogger android.app.Application  	Exception android.app.Application  FacebookSdk android.app.Application  activateApp android.app.Application  onCreate android.app.Application  println android.app.Application  
sdkInitialize android.app.Application  AppEventsLogger android.content.Context  	Exception android.content.Context  FacebookSdk android.content.Context  GeneratedPluginRegistrant android.content.Context  activateApp android.content.Context  println android.content.Context  
sdkInitialize android.content.Context  AppEventsLogger android.content.ContextWrapper  	Exception android.content.ContextWrapper  FacebookSdk android.content.ContextWrapper  GeneratedPluginRegistrant android.content.ContextWrapper  activateApp android.content.ContextWrapper  applicationContext android.content.ContextWrapper  println android.content.ContextWrapper  
sdkInitialize android.content.ContextWrapper  GeneratedPluginRegistrant  android.view.ContextThemeWrapper  println  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  	Companion #androidx.activity.ComponentActivity  
FlutterEngine #androidx.activity.ComponentActivity  GeneratedPluginRegistrant #androidx.activity.ComponentActivity  println #androidx.activity.ComponentActivity  GeneratedPluginRegistrant -androidx.activity.ComponentActivity.Companion  println -androidx.activity.ComponentActivity.Companion  
FlutterEngine #androidx.core.app.ComponentActivity  GeneratedPluginRegistrant #androidx.core.app.ComponentActivity  println #androidx.core.app.ComponentActivity  GeneratedPluginRegistrant &androidx.fragment.app.FragmentActivity  println &androidx.fragment.app.FragmentActivity  FacebookSdk com.facebook  
sdkInitialize com.facebook.FacebookSdk  AppEventsLogger com.facebook.appevents  	Companion &com.facebook.appevents.AppEventsLogger  activateApp &com.facebook.appevents.AppEventsLogger  activateApp 0com.facebook.appevents.AppEventsLogger.Companion  AppEventsLogger com.velvete.ly  Application com.velvete.ly  	Exception com.velvete.ly  FacebookSdk com.velvete.ly  
FlutterEngine com.velvete.ly  FlutterFragmentActivity com.velvete.ly  GeneratedPluginRegistrant com.velvete.ly  MainActivity com.velvete.ly  MainApplication com.velvete.ly  activateApp com.velvete.ly  println com.velvete.ly  
sdkInitialize com.velvete.ly  GeneratedPluginRegistrant com.velvete.ly.MainActivity  println com.velvete.ly.MainActivity  AppEventsLogger com.velvete.ly.MainApplication  FacebookSdk com.velvete.ly.MainApplication  activateApp com.velvete.ly.MainApplication  applicationContext com.velvete.ly.MainApplication  println com.velvete.ly.MainApplication  
sdkInitialize com.velvete.ly.MainApplication  FlutterFragmentActivity io.flutter.embedding.android  GeneratedPluginRegistrant 4io.flutter.embedding.android.FlutterFragmentActivity  configureFlutterEngine 4io.flutter.embedding.android.FlutterFragmentActivity  println 4io.flutter.embedding.android.FlutterFragmentActivity  
FlutterEngine io.flutter.embedding.engine  GeneratedPluginRegistrant io.flutter.plugins  registerWith ,io.flutter.plugins.GeneratedPluginRegistrant  	Exception 	java.lang  message java.lang.Exception  message kotlin.Throwable  println 	kotlin.io                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 