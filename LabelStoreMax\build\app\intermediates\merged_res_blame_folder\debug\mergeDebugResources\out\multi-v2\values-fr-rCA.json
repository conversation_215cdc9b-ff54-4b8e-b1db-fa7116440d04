{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-113:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,145,251,318,386,451,526", "endColumns": "89,105,66,67,64,74,67", "endOffsets": "140,246,313,381,446,521,589"}, "to": {"startLines": "198,199,200,201,202,203,204", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "19503,19593,19699,19766,19834,19899,19974", "endColumns": "89,105,66,67,64,74,67", "endOffsets": "19588,19694,19761,19829,19894,19969,20037"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,210,272,348,433,494,561,612,697,785,872,976,1080,1167,1247,1329,1413,1517,1612,1682,1774,1863,1948,2055,2137,2229,2306,2405,2488,2595,2695,2791,2883,2993,3075,3163,3285,3400,3483,3548,4311,5050,5129,5243,5354,5409,5520,5632,5706,5812,5918,5974,6062,6112,6195,6313,6388,6465,6532,6598,6646,6734,6836,6915,6963,7012,7081,7139,7204,7405,7601,7750,7818,7907,7993,8101,8200,8301,8384,8480,8560,8674,8756,8857,8911,9056,9108,9163,9232,9302,9377,9447,9521,9611,9687,9743", "endColumns": "72,81,61,75,84,60,66,50,84,87,86,103,103,86,79,81,83,103,94,69,91,88,84,106,81,91,76,98,82,106,99,95,91,109,81,87,121,114,82,64,762,738,78,113,110,54,110,111,73,105,105,55,87,49,82,117,74,76,66,65,47,87,101,78,47,48,68,57,64,200,195,148,67,88,85,107,98,100,82,95,79,113,81,100,53,144,51,54,68,69,74,69,73,89,75,55,78", "endOffsets": "123,205,267,343,428,489,556,607,692,780,867,971,1075,1162,1242,1324,1408,1512,1607,1677,1769,1858,1943,2050,2132,2224,2301,2400,2483,2590,2690,2786,2878,2988,3070,3158,3280,3395,3478,3543,4306,5045,5124,5238,5349,5404,5515,5627,5701,5807,5913,5969,6057,6107,6190,6308,6383,6460,6527,6593,6641,6729,6831,6910,6958,7007,7076,7134,7199,7400,7596,7745,7813,7902,7988,8096,8195,8296,8379,8475,8555,8669,8751,8852,8906,9051,9103,9158,9227,9297,9372,9442,9516,9606,9682,9738,9817"}, "to": {"startLines": "205,206,207,208,209,210,211,215,216,217,218,221,223,224,226,231,235,250,253,254,255,257,258,259,261,265,266,267,268,269,270,271,272,273,274,276,279,280,286,287,288,299,300,301,302,303,304,305,306,307,308,309,310,317,318,319,322,323,324,325,329,334,335,336,337,338,343,344,345,346,347,348,352,353,362,363,365,366,370,371,373,378,385,386,457,458,459,461,466,479,480,481,482,483,484,485,501", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20042,20115,20197,20259,20335,20420,20481,20797,20848,20933,21021,21255,21441,21545,21699,22056,22344,23378,23620,23715,23785,23944,24033,24118,24285,24586,24678,24755,24854,24937,25044,25144,25240,25332,25442,25605,25883,26005,26789,26872,26937,28690,29429,29508,29622,29733,29788,29899,30011,30085,30191,30297,30353,30986,31036,31119,31399,31474,31551,31618,32146,32547,32635,32737,32816,32864,33177,33246,33304,33369,33570,33766,34116,34184,35113,35199,35395,35494,35912,35995,36162,36807,37386,37468,45440,45494,45639,45805,46412,48857,48927,49002,49072,49146,49236,49312,50837", "endColumns": "72,81,61,75,84,60,66,50,84,87,86,103,103,86,79,81,83,103,94,69,91,88,84,106,81,91,76,98,82,106,99,95,91,109,81,87,121,114,82,64,762,738,78,113,110,54,110,111,73,105,105,55,87,49,82,117,74,76,66,65,47,87,101,78,47,48,68,57,64,200,195,148,67,88,85,107,98,100,82,95,79,113,81,100,53,144,51,54,68,69,74,69,73,89,75,55,78", "endOffsets": "20110,20192,20254,20330,20415,20476,20543,20843,20928,21016,21103,21354,21540,21627,21774,22133,22423,23477,23710,23780,23872,24028,24113,24220,24362,24673,24750,24849,24932,25039,25139,25235,25327,25437,25519,25688,26000,26115,26867,26932,27695,29424,29503,29617,29728,29783,29894,30006,30080,30186,30292,30348,30436,31031,31114,31232,31469,31546,31613,31679,32189,32630,32732,32811,32859,32908,33241,33299,33364,33565,33761,33910,34179,34268,35194,35302,35489,35590,35990,36086,36237,36916,37463,37564,45489,45634,45686,45855,46476,48922,48997,49067,49141,49231,49307,49363,50911"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca6706086df4d54b31c3004876d79fd4\\transformed\\jetified-facebook-login-18.0.3\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,218,381,542,655,743,830,908,998,1095,1210,1324,1422,1520,1629,1745,1828,1913,2113,2207,2317,2439,2551", "endColumns": "162,162,160,112,87,86,77,89,96,114,113,97,97,108,115,82,84,199,93,109,121,111,173", "endOffsets": "213,376,537,650,738,825,903,993,1090,1205,1319,1417,1515,1624,1740,1823,1908,2108,2202,2312,2434,2546,2720"}, "to": {"startLines": "53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5105,5268,5431,5592,5705,5793,5880,5958,6048,6145,6260,6374,6472,6570,6679,6795,6878,6963,7163,7257,7367,7489,7601", "endColumns": "162,162,160,112,87,86,77,89,96,114,113,97,97,108,115,82,84,199,93,109,121,111,173", "endOffsets": "5263,5426,5587,5700,5788,5875,5953,6043,6140,6255,6369,6467,6565,6674,6790,6873,6958,7158,7252,7362,7484,7596,7770"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "156", "endOffsets": "355"}, "to": {"startLines": "84", "startColumns": "4", "startOffsets": "8838", "endColumns": "160", "endOffsets": "8994"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,390,498,580,681,778,878,1000,1085,1150,1216,1313,1393,1455,1547,1614,1688,1749,1828,1892,1946,2062,2121,2183,2237,2319,2448,2540,2615,2710,2791,2875,3019,3098,3179,3326,3419,3498,3553,3604,3670,3749,3830,3901,3981,4053,4131,4206,4278,4389,4486,4563,4661,4759,4837,4918,5018,5075,5159,5225,5308,5395,5457,5521,5584,5660,5762,5869,5966,6072,6131,6186,6275,6362,6439", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,106,107,81,100,96,99,121,84,64,65,96,79,61,91,66,73,60,78,63,53,115,58,61,53,81,128,91,74,94,80,83,143,78,80,146,92,78,54,50,65,78,80,70,79,71,77,74,71,110,96,76,97,97,77,80,99,56,83,65,82,86,61,63,62,75,101,106,96,105,58,54,88,86,76,80", "endOffsets": "278,385,493,575,676,773,873,995,1080,1145,1211,1308,1388,1450,1542,1609,1683,1744,1823,1887,1941,2057,2116,2178,2232,2314,2443,2535,2610,2705,2786,2870,3014,3093,3174,3321,3414,3493,3548,3599,3665,3744,3825,3896,3976,4048,4126,4201,4273,4384,4481,4558,4656,4754,4832,4913,5013,5070,5154,5220,5303,5390,5452,5516,5579,5655,5757,5864,5961,6067,6126,6181,6270,6357,6434,6515"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3318,3425,3533,3615,3716,4536,4636,4758,10942,11007,11247,13154,13408,13470,13562,13629,13703,13764,13843,13907,13961,14077,14136,14198,14252,14334,14463,14555,14630,14725,14806,14890,15034,15113,15194,15341,15434,15513,15568,15619,15685,15764,15845,15916,15996,16068,16146,16221,16293,16404,16501,16578,16676,16774,16852,16933,17033,17090,17174,17240,17323,17410,17472,17536,17599,17675,17777,17884,17981,18087,18146,18596,19081,19168,19321", "endLines": "5,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "endColumns": "12,106,107,81,100,96,99,121,84,64,65,96,79,61,91,66,73,60,78,63,53,115,58,61,53,81,128,91,74,94,80,83,143,78,80,146,92,78,54,50,65,78,80,70,79,71,77,74,71,110,96,76,97,97,77,80,99,56,83,65,82,86,61,63,62,75,101,106,96,105,58,54,88,86,76,80", "endOffsets": "328,3420,3528,3610,3711,3808,4631,4753,4838,11002,11068,11339,13229,13465,13557,13624,13698,13759,13838,13902,13956,14072,14131,14193,14247,14329,14458,14550,14625,14720,14801,14885,15029,15108,15189,15336,15429,15508,15563,15614,15680,15759,15840,15911,15991,16063,16141,16216,16288,16399,16496,16573,16671,16769,16847,16928,17028,17085,17169,17235,17318,17405,17467,17531,17594,17670,17772,17879,17976,18082,18141,18196,18680,19163,19240,19397"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,325,426,637,684,756,860,935,1208,1271,1366,1440,1495,1559,1632,1721,2045,2112,2179,2234,2289,2383,2533,2646,2705,2792,2880,2973,3044,3147,3449,3530,3609,3686,3748,3810,3888,3963,4042,4151,4256,4355,4473,4576,4650,4729,4820,5018,5228,5361,5508,5570,6437,6545,6610", "endColumns": "106,162,100,210,46,71,103,74,272,62,94,73,54,63,72,88,323,66,66,54,54,93,149,112,58,86,87,92,70,102,301,80,78,76,61,61,77,74,78,108,104,98,117,102,73,78,90,197,209,132,146,61,866,107,64,57", "endOffsets": "157,320,421,632,679,751,855,930,1203,1266,1361,1435,1490,1554,1627,1716,2040,2107,2174,2229,2284,2378,2528,2641,2700,2787,2875,2968,3039,3142,3444,3525,3604,3681,3743,3805,3883,3958,4037,4146,4251,4350,4468,4571,4645,4724,4815,5013,5223,5356,5503,5565,6432,6540,6605,6663"}, "to": {"startLines": "281,282,283,285,289,290,291,292,293,294,295,311,314,316,320,321,326,332,333,341,351,354,355,356,357,358,364,367,372,374,375,376,377,380,382,383,387,391,392,394,396,408,433,434,435,436,437,455,462,463,464,465,467,468,469,486", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "26120,26227,26390,26578,27700,27747,27819,27923,27998,28271,28334,30441,30715,30922,31237,31310,31684,32413,32480,33060,34061,34273,34367,34517,34630,34689,35307,35595,36091,36242,36345,36647,36728,36996,37197,37259,37569,38190,38265,38434,38653,40482,42982,43100,43203,43277,43356,45146,45860,46070,46203,46350,46481,47348,47456,49368", "endColumns": "106,162,100,210,46,71,103,74,272,62,94,73,54,63,72,88,323,66,66,54,54,93,149,112,58,86,87,92,70,102,301,80,78,76,61,61,77,74,78,108,104,98,117,102,73,78,90,197,209,132,146,61,866,107,64,57", "endOffsets": "26222,26385,26486,26784,27742,27814,27918,27993,28266,28329,28424,30510,30765,30981,31305,31394,32003,32475,32542,33110,34111,34362,34512,34625,34684,34771,35390,35683,36157,36340,36642,36723,36802,37068,37254,37316,37642,38260,38339,38538,38753,40576,43095,43198,43272,43351,43442,45339,46065,46198,46345,46407,47343,47451,47516,49421"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,197,259,341,403,474,532,614,687,754,814", "endColumns": "81,59,61,81,61,70,57,81,72,66,59,69", "endOffsets": "132,192,254,336,398,469,527,609,682,749,809,879"}, "to": {"startLines": "220,230,232,233,234,238,246,249,252,256,260,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21173,21996,22138,22200,22282,22573,23104,23296,23547,23877,24225,24516", "endColumns": "81,59,61,81,61,70,57,81,72,66,59,69", "endOffsets": "21250,22051,22195,22277,22339,22639,23157,23373,23615,23939,24280,24581"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,94", "endOffsets": "138,233"}, "to": {"startLines": "522,523", "startColumns": "4,4", "startOffsets": "52895,52983", "endColumns": "87,94", "endOffsets": "52978,53073"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,390,496,583,663,757,849,936,1007,1075,1156,1241,1317,1396,1465", "endColumns": "98,87,97,105,86,79,93,91,86,70,67,80,84,75,78,68,121", "endOffsets": "199,287,385,491,578,658,752,844,931,1002,1070,1151,1236,1312,1391,1460,1582"}, "to": {"startLines": "50,51,97,99,101,122,123,183,184,186,187,190,191,195,519,520,521", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4843,4942,10485,10675,10855,13234,13314,18201,18293,18457,18528,18828,18909,19245,52625,52704,52773", "endColumns": "98,87,97,105,86,79,93,91,86,70,67,80,84,75,78,68,121", "endOffsets": "4937,5025,10578,10776,10937,13309,13403,18288,18375,18523,18591,18904,18989,19316,52699,52768,52890"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\af486666b169eb5d50216ab5d4cc9553\\transformed\\biometric-1.1.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,265,386,549,703,837,968,1148,1251,1385,1548", "endColumns": "117,91,120,162,153,133,130,179,102,133,162,139", "endOffsets": "168,260,381,544,698,832,963,1143,1246,1380,1543,1683"}, "to": {"startLines": "94,98,110,111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10190,10583,11670,11791,11954,12108,12242,12373,12553,12656,12790,12953", "endColumns": "117,91,120,162,153,133,130,179,102,133,162,139", "endOffsets": "10303,10670,11786,11949,12103,12237,12368,12548,12651,12785,12948,13088"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "206,263", "endColumns": "56,74", "endOffsets": "262,337"}, "to": {"startLines": "120,526", "startColumns": "4,4", "startOffsets": "13093,53244", "endColumns": "60,78", "endOffsets": "13149,53318"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e3c1b17a9ffaafd0471e6527f4794f6b\\transformed\\jetified-credentials-1.5.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,171", "endColumns": "115,118", "endOffsets": "166,285"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3083,3199", "endColumns": "115,118", "endOffsets": "3194,3313"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,204", "endColumns": "74,73,76", "endOffsets": "125,199,276"}, "to": {"startLines": "52,100,105", "startColumns": "4,4,4", "startOffsets": "5030,10781,11170", "endColumns": "74,73,76", "endOffsets": "5100,10850,11242"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "96,107,108,109", "startColumns": "4,4,4,4", "startOffsets": "10378,11344,11446,11565", "endColumns": "106,101,118,104", "endOffsets": "10480,11441,11560,11665"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\587767e501a9ab66a3f91617d285250f\\transformed\\core-1.16.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "40,41,42,43,44,45,46,197", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3813,3911,4013,4112,4214,4318,4422,19402", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "3906,4008,4107,4209,4313,4417,4531,19498"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,221,304,391,485,569,652,804,891,1007,1110,1247,1322,1446,1511,1682,1881,2054,2144,2254,2356,2458,2653,2780,2892,3153,3264,3406,3631,3880,3978,4112,4314,4413,4474,4540,4625,4727,4830,4905,5005,5097,5202,5446,5521,5593,5674,5808,5883,5967,6034,6118,6189,6258,6379,6476,6589,6692,6769,6865,6936,7031,7096,7211,7328,7439,7579,7644,7743,7855,8006,8078,8174,8288,8350,8408,8487,8611,8791,9109,9445,9544,9624,9725,9801,9926,10083,10172,10265,10335,10422,10514,10611,10742,10884,10963,11035,11090,11253,11322,11383,11468,11544,11622,11716,11863,11981,12090,12182,12258,12340,12417", "endColumns": "78,86,82,86,93,83,82,151,86,115,102,136,74,123,64,170,198,172,89,109,101,101,194,126,111,260,110,141,224,248,97,133,201,98,60,65,84,101,102,74,99,91,104,243,74,71,80,133,74,83,66,83,70,68,120,96,112,102,76,95,70,94,64,114,116,110,139,64,98,111,150,71,95,113,61,57,78,123,179,317,335,98,79,100,75,124,156,88,92,69,86,91,96,130,141,78,71,54,162,68,60,84,75,77,93,146,117,108,91,75,81,76,157", "endOffsets": "129,216,299,386,480,564,647,799,886,1002,1105,1242,1317,1441,1506,1677,1876,2049,2139,2249,2351,2453,2648,2775,2887,3148,3259,3401,3626,3875,3973,4107,4309,4408,4469,4535,4620,4722,4825,4900,5000,5092,5197,5441,5516,5588,5669,5803,5878,5962,6029,6113,6184,6253,6374,6471,6584,6687,6764,6860,6931,7026,7091,7206,7323,7434,7574,7639,7738,7850,8001,8073,8169,8283,8345,8403,8482,8606,8786,9104,9440,9539,9619,9720,9796,9921,10078,10167,10260,10330,10417,10509,10606,10737,10879,10958,11030,11085,11248,11317,11378,11463,11539,11617,11711,11858,11976,12085,12177,12253,12335,12412,12570"}, "to": {"startLines": "212,213,214,284,296,297,298,315,328,330,331,361,379,381,384,388,389,390,393,395,397,398,399,400,401,402,403,404,405,406,407,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,456,460,470,471,472,473,474,475,476,477,478,487,488,489,490,491,492,493,494,495,496,497,498,499,500,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20548,20627,20714,26491,28429,28523,28607,30770,32059,32194,32310,34976,36921,37073,37321,37647,37818,38017,38344,38543,38758,38860,38962,39157,39284,39396,39657,39768,39910,40135,40384,40581,40715,40917,41016,41077,41143,41228,41330,41433,41508,41608,41700,41805,42049,42124,42196,42277,42411,42486,42570,42637,42721,42792,42861,43447,43544,43657,43760,43837,43933,44004,44099,44164,44279,44396,44507,44647,44712,44811,44923,45074,45344,45691,47521,47583,47641,47720,47844,48024,48342,48678,48777,49426,49527,49603,49728,49885,49974,50067,50137,50224,50316,50413,50544,50686,50765,50916,50971,51134,51203,51264,51349,51425,51503,51597,51744,51862,51971,52063,52139,52221,52298", "endColumns": "78,86,82,86,93,83,82,151,86,115,102,136,74,123,64,170,198,172,89,109,101,101,194,126,111,260,110,141,224,248,97,133,201,98,60,65,84,101,102,74,99,91,104,243,74,71,80,133,74,83,66,83,70,68,120,96,112,102,76,95,70,94,64,114,116,110,139,64,98,111,150,71,95,113,61,57,78,123,179,317,335,98,79,100,75,124,156,88,92,69,86,91,96,130,141,78,71,54,162,68,60,84,75,77,93,146,117,108,91,75,81,76,157", "endOffsets": "20622,20709,20792,26573,28518,28602,28685,30917,32141,32305,32408,35108,36991,37192,37381,37813,38012,38185,38429,38648,38855,38957,39152,39279,39391,39652,39763,39905,40130,40379,40477,40710,40912,41011,41072,41138,41223,41325,41428,41503,41603,41695,41800,42044,42119,42191,42272,42406,42481,42565,42632,42716,42787,42856,42977,43539,43652,43755,43832,43928,43999,44094,44159,44274,44391,44502,44642,44707,44806,44918,45069,45141,45435,45800,47578,47636,47715,47839,48019,48337,48673,48772,48852,49522,49598,49723,49880,49969,50062,50132,50219,50311,50408,50539,50681,50760,50832,50966,51129,51198,51259,51344,51420,51498,51592,51739,51857,51966,52058,52134,52216,52293,52451"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\97b0e46e5034b62169defac2cb4fe8fb\\transformed\\preference-1.2.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,272,349,492,661,747", "endColumns": "69,96,76,142,168,85,79", "endOffsets": "170,267,344,487,656,742,822"}, "to": {"startLines": "95,104,185,189,518,524,525", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "10308,11073,18380,18685,52456,53078,53164", "endColumns": "69,96,76,142,168,85,79", "endOffsets": "10373,11165,18452,18823,52620,53159,53239"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,202,269,354,425,486,558,631,695,763,833,893,953,1027,1091,1162,1225,1290,1355,1439,1520,1612,1710,1828,1910,1961,2011,2108,2170,2245,2316,2427,2516,2628", "endColumns": "64,81,66,84,70,60,71,72,63,67,69,59,59,73,63,70,62,64,64,83,80,91,97,117,81,50,49,96,61,74,70,110,88,111,111", "endOffsets": "115,197,264,349,420,481,553,626,690,758,828,888,948,1022,1086,1157,1220,1285,1350,1434,1515,1607,1705,1823,1905,1956,2006,2103,2165,2240,2311,2422,2511,2623,2735"}, "to": {"startLines": "219,222,225,227,228,229,236,237,239,240,241,242,243,244,245,247,248,251,262,263,275,277,278,312,313,327,339,340,342,349,350,359,360,368,369", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21108,21359,21632,21779,21864,21935,22428,22500,22644,22708,22776,22846,22906,22966,23040,23162,23233,23482,24367,24432,25524,25693,25785,30515,30633,32008,32913,32963,33115,33915,33990,34776,34887,35688,35800", "endColumns": "64,81,66,84,70,60,71,72,63,67,69,59,59,73,63,70,62,64,64,83,80,91,97,117,81,50,49,96,61,74,70,110,88,111,111", "endOffsets": "21168,21436,21694,21859,21930,21991,22495,22568,22703,22771,22841,22901,22961,23035,23099,23228,23291,23542,24427,24511,25600,25780,25878,30628,30710,32054,32958,33055,33172,33985,34056,34882,34971,35795,35907"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,323,433,520,626,756,841,921,1012,1105,1203,1298,1398,1491,1584,1679,1770,1861,1947,2057,2168,2271,2382,2490,2597,2756,2855", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,318,428,515,621,751,836,916,1007,1100,1198,1293,1393,1486,1579,1674,1765,1856,1942,2052,2163,2266,2377,2485,2592,2751,2850,2937"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,444,551,661,748,854,984,1069,1149,1240,1333,1431,1526,1626,1719,1812,1907,1998,2089,2175,2285,2396,2499,2610,2718,2825,2984,18994", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "439,546,656,743,849,979,1064,1144,1235,1328,1426,1521,1621,1714,1807,1902,1993,2084,2170,2280,2391,2494,2605,2713,2820,2979,3078,19076"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,299,475,601,706,873,1002,1119,1228,1419,1527,1708,1840,1996,2171,2240,2303", "endColumns": "101,175,125,104,166,128,116,108,190,107,180,131,155,174,68,62,79", "endOffsets": "298,474,600,705,872,1001,1118,1227,1418,1526,1707,1839,1995,2170,2239,2302,2382"}, "to": {"startLines": "76,77,78,79,80,81,82,83,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7775,7881,8061,8191,8300,8471,8604,8725,8999,9194,9306,9491,9627,9787,9966,10039,10106", "endColumns": "105,179,129,108,170,132,120,112,194,111,184,135,159,178,72,66,83", "endOffsets": "7876,8056,8186,8295,8466,8599,8720,8833,9189,9301,9486,9622,9782,9961,10034,10101,10185"}}]}]}