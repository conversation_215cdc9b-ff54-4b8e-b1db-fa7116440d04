# Official velvete e-commerce App

# Velvete Store
# Version: 1.2.2
# Author: we<PERSON><PERSON> <PERSON>.
# Homepage: https://velvete.ly
# Documentation: https://velvete.ly/docs

### Change App Icon
# 1 Replace: public/icon/appicon.png (1024px1024px icon size)
# 2 Run this command from the terminal: "dart run flutter_launcher_icons:main"

### Uploading the IOS/Android app
# IOS https://flutter.dev/docs/deployment/ios
# Android https://flutter.dev/docs/deployment/android

name: velvete_store
description: Velvete Store

publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: '>=3.4.0 <4.0.0'
  flutter: ">=3.24.0 <4.0.0"

dependencies:
  google_fonts: any
  analyzer: any
  intl: any
  flutter_staggered_grid_view: any
  nylo_framework: any
  woocommerce_flutter_api: any
  wp_json_api: any
  cached_network_image: any
  package_info_plus: any
  flutter_inappwebview: any
  pull_to_refresh_flutter3: any
  url_launcher: any
  bubble_tab_indicator: any
  math_expressions: any
  validated: any
  flutter_spinkit: any
  auto_size_text: any
  html: any
  flutter_widget_from_html_core: any
  flutter_rating_bar: any
  flutter_swiper_view: any
  carousel_slider: any
  firebase_messaging: any
  firebase_core: any
  collection: any
  flutter_stripe: any
  pretty_dio_logger: any
  animate_do: any
  google_maps_flutter: any
  geolocator: any
  geocoding: any
  image_picker: any
  http: any
  lottie: any
  # Social Login Dependencies
  google_sign_in: any
  flutter_facebook_auth: any
  sign_in_with_apple: any
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  cupertino_icons: any

dev_dependencies:
  flutter_launcher_icons: any
  lints: any
  flutter_test:
    sdk: flutter
  flutter_lints: any

# APP ICON
flutter_launcher_icons:
  android: true
  ios: true
  image_path: "public/app_icon/appicon.png"
  remove_alpha_ios: true

flutter:

  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - public/fonts/
    - public/
    - public/json/
    - public/images/ # Corrected asset path for banner images
    - public/home page image carousel/ # New directory for carousel images
    - public/animations/
    - lang/
    - .env

  fonts:
    - family: Almaria
      fonts:
        - asset: public/fonts/Almaria-Regular.ttf
