{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-113:/values-ja/values-ja.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,81", "endOffsets": "135,217"}, "to": {"startLines": "523,524", "startColumns": "4,4", "startOffsets": "43079,43164", "endColumns": "84,81", "endOffsets": "43159,43241"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,117,189,251,318,389,462,534,627,694,768,846,933,989,1078,1137,1277,1422,1530,1615,1697,1784,1871,1984,2071,2158,2295,2389,2493,2658,2829,2925,3019,3139,3218,3276,3337,3415,3495,3576,3643,3724,3803,3889,4028,4095,4159,4222,4318,4391,4468,4532,4604,4675,4743,4830,4911,5007,5089,5156,5234,5299,5371,5427,5514,5602,5690,5798,5857,5937,6027,6134,6202,6274,6357,6410,6464,6528,6609,6753,6956,7174,7260,7326,7396,7459,7545,7636,7715,7787,7857,7928,7998,8077,8160,8262,8333,8396,8447,8579,8642,8698,8767,8829,8901,8968,9081,9171,9252,9329,9397,9465,9526", "endColumns": "61,71,61,66,70,72,71,92,66,73,77,86,55,88,58,139,144,107,84,81,86,86,112,86,86,136,93,103,164,170,95,93,119,78,57,60,77,79,80,66,80,78,85,138,66,63,62,95,72,76,63,71,70,67,86,80,95,81,66,77,64,71,55,86,87,87,107,58,79,89,106,67,71,82,52,53,63,80,143,202,217,85,65,69,62,85,90,78,71,69,70,69,78,82,101,70,62,50,131,62,55,68,61,71,66,112,89,80,76,67,67,60,114", "endOffsets": "112,184,246,313,384,457,529,622,689,763,841,928,984,1073,1132,1272,1417,1525,1610,1692,1779,1866,1979,2066,2153,2290,2384,2488,2653,2824,2920,3014,3134,3213,3271,3332,3410,3490,3571,3638,3719,3798,3884,4023,4090,4154,4217,4313,4386,4463,4527,4599,4670,4738,4825,4906,5002,5084,5151,5229,5294,5366,5422,5509,5597,5685,5793,5852,5932,6022,6129,6197,6269,6352,6405,6459,6523,6604,6748,6951,7169,7255,7321,7391,7454,7540,7631,7710,7782,7852,7923,7993,8072,8155,8257,8328,8391,8442,8574,8637,8693,8762,8824,8896,8963,9076,9166,9247,9324,9392,9460,9521,9636"}, "to": {"startLines": "212,213,214,284,296,297,298,315,328,330,331,362,380,382,385,389,390,391,394,396,398,399,400,401,402,403,404,405,406,407,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,457,461,471,472,473,474,475,476,477,478,479,488,489,490,491,492,493,494,495,496,497,498,499,500,501,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17914,17976,18048,23062,24390,24461,24534,26112,27137,27249,27323,29481,30889,31000,31208,31480,31620,31765,32008,32183,32346,32433,32520,32633,32720,32807,32944,33038,33142,33307,33478,33653,33747,33867,33946,34004,34065,34143,34223,34304,34371,34452,34531,34617,34756,34823,34887,34950,35046,35119,35196,35260,35332,35403,35471,35990,36071,36167,36249,36316,36394,36459,36531,36587,36674,36762,36850,36958,37017,37097,37187,37294,37487,37747,38818,38871,38925,38989,39070,39214,39417,39635,39721,40276,40346,40409,40495,40586,40665,40737,40807,40878,40948,41027,41110,41212,41283,41408,41459,41591,41654,41710,41779,41841,41913,41980,42093,42183,42264,42341,42409,42477,42538", "endColumns": "61,71,61,66,70,72,71,92,66,73,77,86,55,88,58,139,144,107,84,81,86,86,112,86,86,136,93,103,164,170,95,93,119,78,57,60,77,79,80,66,80,78,85,138,66,63,62,95,72,76,63,71,70,67,86,80,95,81,66,77,64,71,55,86,87,87,107,58,79,89,106,67,71,82,52,53,63,80,143,202,217,85,65,69,62,85,90,78,71,69,70,69,78,82,101,70,62,50,131,62,55,68,61,71,66,112,89,80,76,67,67,60,114", "endOffsets": "17971,18043,18105,23124,24456,24529,24601,26200,27199,27318,27396,29563,30940,31084,31262,31615,31760,31868,32088,32260,32428,32515,32628,32715,32802,32939,33033,33137,33302,33473,33569,33742,33862,33941,33999,34060,34138,34218,34299,34366,34447,34526,34612,34751,34818,34882,34945,35041,35114,35191,35255,35327,35398,35466,35553,36066,36162,36244,36311,36389,36454,36526,36582,36669,36757,36845,36953,37012,37092,37182,37289,37357,37554,37825,38866,38920,38984,39065,39209,39412,39630,39716,39782,40341,40404,40490,40581,40660,40732,40802,40873,40943,41022,41105,41207,41278,41341,41454,41586,41649,41705,41774,41836,41908,41975,42088,42178,42259,42336,42404,42472,42533,42648"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,253,320,384,453,534,616,701,805,881,934,997,1081,1145,1203,1284,1345,1409,1464,1523,1580,1634,1727,1783,1840,1894,1960,2060,2136,2207,2286,2359,2440,2562,2624,2686,2787,2866,2941,2994,3045,3111,3181,3251,3322,3392,3456,3527,3595,3658,3749,3828,3891,3971,4053,4125,4196,4268,4316,4388,4452,4527,4604,4666,4730,4793,4860,4946,5032,5113,5196,5253,5308,5381,5459,5532", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,66,63,68,80,81,84,103,75,52,62,83,63,57,80,60,63,54,58,56,53,92,55,56,53,65,99,75,70,78,72,80,121,61,61,100,78,74,52,50,65,69,69,70,69,63,70,67,62,90,78,62,79,81,71,70,71,47,71,63,74,76,61,63,62,66,85,85,80,82,56,54,72,77,72,70", "endOffsets": "248,315,379,448,529,611,696,800,876,929,992,1076,1140,1198,1279,1340,1404,1459,1518,1575,1629,1722,1778,1835,1889,1955,2055,2131,2202,2281,2354,2435,2557,2619,2681,2782,2861,2936,2989,3040,3106,3176,3246,3317,3387,3451,3522,3590,3653,3744,3823,3886,3966,4048,4120,4191,4263,4311,4383,4447,4522,4599,4661,4725,4788,4855,4941,5027,5108,5191,5248,5303,5376,5454,5527,5598"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3109,3176,3240,3309,3390,4141,4226,4330,9586,9639,9859,11408,11628,11686,11767,11828,11892,11947,12006,12063,12117,12210,12266,12323,12377,12443,12543,12619,12690,12769,12842,12923,13045,13107,13169,13270,13349,13424,13477,13528,13594,13664,13734,13805,13875,13939,14010,14078,14141,14232,14311,14374,14454,14536,14608,14679,14751,14799,14871,14935,15010,15087,15149,15213,15276,15343,15429,15515,15596,15679,15736,16149,16579,16657,16799", "endLines": "5,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "endColumns": "12,66,63,68,80,81,84,103,75,52,62,83,63,57,80,60,63,54,58,56,53,92,55,56,53,65,99,75,70,78,72,80,121,61,61,100,78,74,52,50,65,69,69,70,69,63,70,67,62,90,78,62,79,81,71,70,71,47,71,63,74,76,61,63,62,66,85,85,80,82,56,54,72,77,72,70", "endOffsets": "298,3171,3235,3304,3385,3467,4221,4325,4401,9634,9697,9938,11467,11681,11762,11823,11887,11942,12001,12058,12112,12205,12261,12318,12372,12438,12538,12614,12685,12764,12837,12918,13040,13102,13164,13265,13344,13419,13472,13523,13589,13659,13729,13800,13870,13934,14005,14073,14136,14227,14306,14369,14449,14531,14603,14674,14746,14794,14866,14930,15005,15082,15144,15208,15271,15338,15424,15510,15591,15674,15731,15786,16217,16652,16725,16865"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\97b0e46e5034b62169defac2cb4fe8fb\\transformed\\preference-1.2.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,172,255,325,453,621,701", "endColumns": "66,82,69,127,167,79,75", "endOffsets": "167,250,320,448,616,696,772"}, "to": {"startLines": "95,104,185,189,519,525,526", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "9001,9702,15951,16222,42653,43246,43326", "endColumns": "66,82,69,127,167,79,75", "endOffsets": "9063,9780,16016,16345,42816,43321,43397"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-ja\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,64", "endOffsets": "258,323"}, "to": {"startLines": "120,527", "startColumns": "4,4", "startOffsets": "11347,43402", "endColumns": "60,68", "endOffsets": "11403,43466"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,269,358,455,538,616,694,779,854,918,982,1056,1132,1201,1277,1342", "endColumns": "85,77,88,96,82,77,77,84,74,63,63,73,75,68,75,64,116", "endOffsets": "186,264,353,450,533,611,689,774,849,913,977,1051,1127,1196,1272,1337,1454"}, "to": {"startLines": "50,51,97,99,101,122,123,183,184,186,187,190,191,195,520,521,522", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4406,4492,9161,9335,9503,11472,11550,15791,15876,16021,16085,16350,16424,16730,42821,42897,42962", "endColumns": "85,77,88,96,82,77,77,84,74,63,63,73,75,68,75,64,116", "endOffsets": "4487,4565,9245,9427,9581,11545,11623,15871,15946,16080,16144,16419,16495,16794,42892,42957,43074"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\587767e501a9ab66a3f91617d285250f\\transformed\\core-1.16.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,724", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,820"}, "to": {"startLines": "40,41,42,43,44,45,46,197", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3472,3564,3664,3758,3854,3947,4040,16870", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "3559,3659,3753,3849,3942,4035,4136,16966"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca6706086df4d54b31c3004876d79fd4\\transformed\\jetified-facebook-login-18.0.3\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,195,308,436,545,632,717,792,880,967,1072,1173,1262,1351,1445,1553,1631,1711,1846,1941,2044,2151,2248", "endColumns": "139,112,127,108,86,84,74,87,86,104,100,88,88,93,107,77,79,134,94,102,106,96,101", "endOffsets": "190,303,431,540,627,712,787,875,962,1067,1168,1257,1346,1440,1548,1626,1706,1841,1936,2039,2146,2243,2345"}, "to": {"startLines": "53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4645,4785,4898,5026,5135,5222,5307,5382,5470,5557,5662,5763,5852,5941,6035,6143,6221,6301,6436,6531,6634,6741,6838", "endColumns": "139,112,127,108,86,84,74,87,86,104,100,88,88,93,107,77,79,134,94,102,106,96,101", "endOffsets": "4780,4893,5021,5130,5217,5302,5377,5465,5552,5657,5758,5847,5936,6030,6138,6216,6296,6431,6526,6629,6736,6833,6935"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "73", "endOffsets": "124"}, "to": {"startLines": "354", "startColumns": "4", "startOffsets": "28870", "endColumns": "73", "endOffsets": "28939"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,148,243,344", "endColumns": "92,94,100,94", "endOffsets": "143,238,339,434"}, "to": {"startLines": "96,107,108,109", "startColumns": "4,4,4,4", "startOffsets": "9068,9943,10038,10139", "endColumns": "92,94,100,94", "endOffsets": "9156,10033,10134,10229"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-ja\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,423,539,641,769,885,986,1081,1211,1308,1437,1552,1668,1784,1840,1895", "endColumns": "99,129,115,101,127,115,100,94,129,96,128,114,115,115,55,54,66", "endOffsets": "292,422,538,640,768,884,985,1080,1210,1307,1436,1551,1667,1783,1839,1894,1961"}, "to": {"startLines": "76,77,78,79,80,81,82,83,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6940,7044,7178,7298,7404,7536,7656,7761,7982,8116,8217,8350,8469,8589,8709,8769,8828", "endColumns": "103,133,119,105,131,119,104,98,133,100,132,118,119,119,59,58,70", "endOffsets": "7039,7173,7293,7399,7531,7651,7756,7855,8111,8212,8345,8464,8584,8704,8764,8823,8894"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,188,252,327,391,452,514,575,635,709,783,850,910,978,1042,1109,1173,1239,1298,1375,1448,1514,1586,1661,1738,1785,1834,1900,1962,2020,2086,2165,2232,2305", "endColumns": "59,72,63,74,63,60,61,60,59,73,73,66,59,67,63,66,63,65,58,76,72,65,71,74,76,46,48,65,61,57,65,78,66,72,71", "endOffsets": "110,183,247,322,386,447,509,570,630,704,778,845,905,973,1037,1104,1168,1234,1293,1370,1443,1509,1581,1656,1733,1780,1829,1895,1957,2015,2081,2160,2227,2300,2372"}, "to": {"startLines": "219,222,225,227,228,229,236,237,239,240,241,242,243,244,245,247,248,251,262,263,275,277,278,312,313,327,339,340,342,349,350,360,361,369,370", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18370,18589,18823,18971,19046,19110,19565,19627,19750,19810,19884,19958,20025,20085,20153,20274,20341,20554,21343,21402,22286,22431,22497,25906,25981,27090,27832,27881,28000,28562,28620,29335,29414,29987,30060", "endColumns": "59,72,63,74,63,60,61,60,59,73,73,66,59,67,63,66,63,65,58,76,72,65,71,74,76,46,48,65,61,57,65,78,66,72,71", "endOffsets": "18425,18657,18882,19041,19105,19166,19622,19683,19805,19879,19953,20020,20080,20148,20212,20336,20400,20615,21397,21474,22354,22492,22564,25976,26053,27132,27876,27942,28057,28615,28681,29409,29476,30055,30127"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,201", "endColumns": "74,70,73", "endOffsets": "125,196,270"}, "to": {"startLines": "52,100,105", "startColumns": "4,4,4", "startOffsets": "4570,9432,9785", "endColumns": "74,70,73", "endOffsets": "4640,9498,9854"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,841,932,1025,1120,1214,1314,1407,1502,1596,1687,1778,1856,1958,2056,2151,2254,2350,2446,2594,2691", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "197,290,395,477,575,683,761,836,927,1020,1115,1209,1309,1402,1497,1591,1682,1773,1851,1953,2051,2146,2249,2345,2441,2589,2686,2765"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "303,400,493,598,680,778,886,964,1039,1130,1223,1318,1412,1512,1605,1700,1794,1885,1976,2054,2156,2254,2349,2452,2548,2644,2792,16500", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "395,488,593,675,773,881,959,1034,1125,1218,1313,1407,1507,1600,1695,1789,1880,1971,2049,2151,2249,2344,2447,2543,2639,2787,2884,16574"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\af486666b169eb5d50216ab5d4cc9553\\transformed\\biometric-1.1.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,157,242,345,458,572,691,797,913,1009,1128,1247", "endColumns": "101,84,102,112,113,118,105,115,95,118,118,107", "endOffsets": "152,237,340,453,567,686,792,908,1004,1123,1242,1350"}, "to": {"startLines": "94,98,110,111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8899,9250,10234,10337,10450,10564,10683,10789,10905,11001,11120,11239", "endColumns": "101,84,102,112,113,118,105,115,95,118,118,107", "endOffsets": "8996,9330,10332,10445,10559,10678,10784,10900,10996,11115,11234,11342"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,189,256,318,388,442,502,555,626,692,762,848,934,1009,1093,1167,1240,1322,1403,1466,1544,1622,1697,1781,1856,1934,2004,2089,2158,2237,2310,2380,2455,2536,2600,2672,2767,2852,2914,2975,3433,3864,3934,4025,4114,4169,4253,4331,4392,4467,4547,4602,4676,4724,4795,4881,4947,5013,5080,5146,5191,5259,5343,5414,5457,5500,5569,5627,5686,5797,5908,6000,6063,6133,6197,6275,6343,6410,6473,6548,6612,6692,6768,6847,6896,6990,7035,7090,7152,7211,7279,7338,7401,7479,7539,7586", "endColumns": "62,70,66,61,69,53,59,52,70,65,69,85,85,74,83,73,72,81,80,62,77,77,74,83,74,77,69,84,68,78,72,69,74,80,63,71,94,84,61,60,457,430,69,90,88,54,83,77,60,74,79,54,73,47,70,85,65,65,66,65,44,67,83,70,42,42,68,57,58,110,110,91,62,69,63,77,67,66,62,74,63,79,75,78,48,93,44,54,61,58,67,58,62,77,59,46,61", "endOffsets": "113,184,251,313,383,437,497,550,621,687,757,843,929,1004,1088,1162,1235,1317,1398,1461,1539,1617,1692,1776,1851,1929,1999,2084,2153,2232,2305,2375,2450,2531,2595,2667,2762,2847,2909,2970,3428,3859,3929,4020,4109,4164,4248,4326,4387,4462,4542,4597,4671,4719,4790,4876,4942,5008,5075,5141,5186,5254,5338,5409,5452,5495,5564,5622,5681,5792,5903,5995,6058,6128,6192,6270,6338,6405,6468,6543,6607,6687,6763,6842,6891,6985,7030,7085,7147,7206,7274,7333,7396,7474,7534,7581,7643"}, "to": {"startLines": "205,206,207,208,209,210,211,215,216,217,218,221,223,224,226,231,235,250,253,254,255,257,258,259,261,265,266,267,268,269,270,271,272,273,274,276,279,280,286,287,288,299,300,301,302,303,304,305,306,307,308,309,310,317,318,319,322,323,324,325,329,334,335,336,337,338,343,344,345,346,347,348,352,353,363,364,366,367,371,372,374,379,386,387,458,459,460,462,467,480,481,482,483,484,485,486,502", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17467,17530,17601,17668,17730,17800,17854,18110,18163,18234,18300,18503,18662,18748,18887,19227,19492,20472,20686,20767,20830,20971,21049,21124,21268,21542,21620,21690,21775,21844,21923,21996,22066,22141,22222,22359,22569,22664,23264,23326,23387,24606,25037,25107,25198,25287,25342,25426,25504,25565,25640,25720,25775,26269,26317,26388,26632,26698,26764,26831,27204,27523,27591,27675,27746,27789,28062,28131,28189,28248,28359,28470,28737,28800,29568,29632,29778,29846,30132,30195,30333,30809,31267,31343,37559,37608,37702,37830,38269,39787,39846,39914,39973,40036,40114,40174,41346", "endColumns": "62,70,66,61,69,53,59,52,70,65,69,85,85,74,83,73,72,81,80,62,77,77,74,83,74,77,69,84,68,78,72,69,74,80,63,71,94,84,61,60,457,430,69,90,88,54,83,77,60,74,79,54,73,47,70,85,65,65,66,65,44,67,83,70,42,42,68,57,58,110,110,91,62,69,63,77,67,66,62,74,63,79,75,78,48,93,44,54,61,58,67,58,62,77,59,46,61", "endOffsets": "17525,17596,17663,17725,17795,17849,17909,18158,18229,18295,18365,18584,18743,18818,18966,19296,19560,20549,20762,20825,20903,21044,21119,21203,21338,21615,21685,21770,21839,21918,21991,22061,22136,22217,22281,22426,22659,22744,23321,23382,23840,25032,25102,25193,25282,25337,25421,25499,25560,25635,25715,25770,25844,26312,26383,26469,26693,26759,26826,26892,27244,27586,27670,27741,27784,27827,28126,28184,28243,28354,28465,28557,28795,28865,29627,29705,29841,29908,30190,30265,30392,30884,31338,31417,37603,37697,37742,37880,38326,39841,39909,39968,40031,40109,40169,40216,41403"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-ja\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "117", "endOffsets": "312"}, "to": {"startLines": "84", "startColumns": "4", "startOffsets": "7860", "endColumns": "121", "endOffsets": "7977"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,134,223,290,357,420,490", "endColumns": "78,88,66,66,62,69,60", "endOffsets": "129,218,285,352,415,485,546"}, "to": {"startLines": "198,199,200,201,202,203,204", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "16971,17050,17139,17206,17273,17336,17406", "endColumns": "78,88,66,66,62,69,60", "endOffsets": "17045,17134,17201,17268,17331,17401,17462"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,142,286,368,503,546,606,686,750,911,968,1048,1105,1159,1223,1294,1381,1574,1635,1696,1749,1800,1871,1973,2060,2114,2191,2259,2333,2396,2472,2664,2734,2808,2863,2921,2982,3040,3104,3175,3265,3346,3425,3527,3616,3695,3773,3857,3982,4116,4215,4312,4366,4713,4796,4853", "endColumns": "86,143,81,134,42,59,79,63,160,56,79,56,53,63,70,86,192,60,60,52,50,70,101,86,53,76,67,73,62,75,191,69,73,54,57,60,57,63,70,89,80,78,101,88,78,77,83,124,133,98,96,53,346,82,56,54", "endOffsets": "137,281,363,498,541,601,681,745,906,963,1043,1100,1154,1218,1289,1376,1569,1630,1691,1744,1795,1866,1968,2055,2109,2186,2254,2328,2391,2467,2659,2729,2803,2858,2916,2977,3035,3099,3170,3260,3341,3420,3522,3611,3690,3768,3852,3977,4111,4210,4307,4361,4708,4791,4848,4903"}, "to": {"startLines": "281,282,283,285,289,290,291,292,293,294,295,311,314,316,320,321,326,332,333,341,351,355,356,357,358,359,365,368,373,375,376,377,378,381,383,384,388,392,393,395,397,409,434,435,436,437,438,456,463,464,465,466,468,469,470,487", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "22749,22836,22980,23129,23845,23888,23948,24028,24092,24253,24310,25849,26058,26205,26474,26545,26897,27401,27462,27947,28686,28944,29015,29117,29204,29258,29710,29913,30270,30397,30473,30665,30735,30945,31089,31147,31422,31873,31937,32093,32265,33574,35558,35660,35749,35828,35906,37362,37885,38019,38118,38215,38331,38678,38761,40221", "endColumns": "86,143,81,134,42,59,79,63,160,56,79,56,53,63,70,86,192,60,60,52,50,70,101,86,53,76,67,73,62,75,191,69,73,54,57,60,57,63,70,89,80,78,101,88,78,77,83,124,133,98,96,53,346,82,56,54", "endOffsets": "22831,22975,23057,23259,23883,23943,24023,24087,24248,24305,24385,25901,26107,26264,26540,26627,27085,27457,27518,27995,28732,29010,29112,29199,29253,29330,29773,29982,30328,30468,30660,30730,30804,30995,31142,31203,31475,31932,32003,32178,32341,33648,35655,35744,35823,35901,35985,37482,38014,38113,38210,38264,38673,38756,38813,40271"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e3c1b17a9ffaafd0471e6527f4794f6b\\transformed\\jetified-credentials-1.5.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,110", "endOffsets": "159,270"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "2889,2998", "endColumns": "108,110", "endOffsets": "2993,3104"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,184,243,317,375,437,494,561,627,690,750", "endColumns": "72,55,58,73,57,61,56,66,65,62,59,62", "endOffsets": "123,179,238,312,370,432,489,556,622,685,745,808"}, "to": {"startLines": "220,230,232,233,234,238,246,249,252,256,260,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18430,19171,19301,19360,19434,19688,20217,20405,20620,20908,21208,21479", "endColumns": "72,55,58,73,57,61,56,66,65,62,59,62", "endOffsets": "18498,19222,19355,19429,19487,19745,20269,20467,20681,20966,21263,21537"}}]}]}