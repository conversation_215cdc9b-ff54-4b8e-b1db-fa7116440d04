1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.velvete.ly"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10    <!--
11         <PERSON><PERSON><PERSON> needs it to communicate with the running application
12         to allow setting breakpoints, to provide hot reload, etc.
13    -->
14    <uses-permission android:name="android.permission.INTERNET" />
14-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:5:5-67
14-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:5:22-64
15    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
15-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:6:5-78
15-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:6:22-76
16    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
16-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:7:5-76
16-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:7:22-73
17    <uses-permission android:name="android.permission.VIBRATE" />
17-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:8:5-65
17-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:8:22-63
18    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
18-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:9:5-79
18-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:9:22-76
19    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" /> <!-- Profile picture functionality permissions -->
19-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:10:5-81
19-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:10:22-78
20    <uses-permission android:name="android.permission.CAMERA" />
20-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:13:5-65
20-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:13:22-62
21    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
21-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:14:5-80
21-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:14:22-77
22    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
22-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:15:5-81
22-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:15:22-78
23    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" /> <!-- Samsung -->
23-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:16:5-76
23-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:16:22-73
24    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
24-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-86
24-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-83
25    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- HTC -->
25-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-87
25-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-84
26    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
26-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-81
26-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:22-78
27    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- Sony -->
27-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-83
27-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:22-80
28    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
28-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:5-88
28-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:22-85
29    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- Apex -->
29-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:5-92
29-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:22-89
30    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- Solid -->
30-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:5-84
30-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:22-81
31    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- Huawei -->
31-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:5-83
31-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:22-80
32    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
32-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:5-91
32-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:22-88
33    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
33-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:5-92
33-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:22-89
34    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" />
34-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:5-93
34-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:22-90
35    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
35-->[:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
35-->[:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
36    <uses-permission android:name="android.permission.WAKE_LOCK" />
36-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
36-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
37
38    <queries>
38-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:15
39        <intent>
39-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
40            <action android:name="android.support.customtabs.action.CustomTabsService" />
40-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-90
40-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-87
41        </intent>
42        <!-- Added to check the default browser that will host the AuthFlow. -->
43        <intent>
43-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:13:9-17:18
44            <action android:name="android.intent.action.VIEW" />
44-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:68:17-69
44-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:68:25-66
45
46            <data android:scheme="http" />
46-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:71:17-75
46-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:71:23-72
47        </intent>
48
49        <package android:name="com.facebook.katana" /> <!-- Added to check if Chrome is installed for browser-based payment authentication (e.g. 3DS1). -->
49-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:16:9-55
49-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:16:18-52
50        <package android:name="com.android.chrome" /> <!-- Needs to be explicitly declared on Android R+ -->
50-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:11:9-54
50-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:11:18-51
51        <package android:name="com.google.android.apps.maps" />
51-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bda770351327ba3421df9753387b33\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
51-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bda770351327ba3421df9753387b33\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
52    </queries>
53
54    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
54-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af486666b169eb5d50216ab5d4cc9553\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
54-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af486666b169eb5d50216ab5d4cc9553\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
55    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
55-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af486666b169eb5d50216ab5d4cc9553\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
55-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af486666b169eb5d50216ab5d4cc9553\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
56    <uses-permission android:name="com.google.android.gms.permission.AD_ID" /> <!-- Support for Google Privacy Sandbox adservices API -->
56-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:14:5-79
56-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:14:22-76
57    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
57-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:16:5-88
57-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:16:22-85
58    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
58-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:17:5-82
58-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:17:22-79
59    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_CUSTOM_AUDIENCE" />
59-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:18:5-92
59-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:18:22-89
60    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
60-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:19:5-83
60-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:19:22-80
61    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
61-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:5-82
61-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:22-79
62
63    <uses-feature
63-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bda770351327ba3421df9753387b33\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
64        android:glEsVersion="0x00020000"
64-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bda770351327ba3421df9753387b33\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
65        android:required="true" />
65-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bda770351327ba3421df9753387b33\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
66
67    <permission
67-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\587767e501a9ab66a3f91617d285250f\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
68        android:name="com.velvete.ly.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
68-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\587767e501a9ab66a3f91617d285250f\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
69        android:protectionLevel="signature" />
69-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\587767e501a9ab66a3f91617d285250f\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
70
71    <uses-permission android:name="com.velvete.ly.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
71-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\587767e501a9ab66a3f91617d285250f\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
71-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\587767e501a9ab66a3f91617d285250f\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
72    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
72-->[com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8d47debb03072941b4414566260c2d\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:5-110
72-->[com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8d47debb03072941b4414566260c2d\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:22-107
73
74    <application
74-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:18:5-74:19
75        android:name="com.velvete.ly.MainApplication"
75-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:19:9-54
76        android:allowBackup="false"
76-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:21:9-36
77        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
77-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\587767e501a9ab66a3f91617d285250f\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
78        android:debuggable="true"
79        android:extractNativeLibs="true"
80        android:fullBackupContent="false"
80-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:22:9-42
81        android:icon="@mipmap/ic_launcher"
81-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:24:9-43
82        android:label="Velvete Store"
82-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:20:9-38
83        android:supportsRtl="true"
83-->[com.facebook.android:facebook-android-sdk:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b9715a8446a004a2a156cc04b2f2340\transformed\jetified-facebook-android-sdk-17.0.2\AndroidManifest.xml:16:18-44
84        android:usesCleartextTraffic="true" >
84-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:23:9-44
85        <activity
85-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:25:9-43:20
86            android:name="com.velvete.ly.MainActivity"
86-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:26:13-55
87            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
87-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:29:13-163
88            android:exported="true"
88-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:33:13-36
89            android:hardwareAccelerated="true"
89-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:30:13-47
90            android:launchMode="singleTop"
90-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:27:13-43
91            android:screenOrientation="portrait"
91-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:31:13-49
92            android:theme="@style/LaunchTheme"
92-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:28:13-47
93            android:windowSoftInputMode="adjustResize" >
93-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:32:13-55
94            <meta-data
94-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:34:13-37:17
95                android:name="io.flutter.embedding.android.NormalTheme"
95-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:35:15-70
96                android:resource="@style/NormalTheme" />
96-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:36:15-52
97
98            <intent-filter>
98-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:39:13-42:29
99                <action android:name="android.intent.action.MAIN" />
99-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:40:17-68
99-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:40:25-66
100
101                <category android:name="android.intent.category.LAUNCHER" />
101-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:41:17-76
101-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:41:27-74
102            </intent-filter>
103        </activity>
104
105        <meta-data
105-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:44:9-46:33
106            android:name="flutterEmbedding"
106-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:45:13-44
107            android:value="2" />
107-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:46:13-30
108        <meta-data
108-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:47:9-49:71
109            android:name="com.google.android.geo.API_KEY"
109-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:48:13-58
110            android:value="AIzaSyDrKt-lB3zOcD_eLMRdnkUSspv1ovnhn6s" />
110-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:49:13-68
111
112        <!-- Facebook Configuration -->
113        <meta-data
113-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:52:9-54:54
114            android:name="com.facebook.sdk.ApplicationId"
114-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:53:13-58
115            android:value="@string/facebook_app_id" />
115-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:54:13-52
116        <meta-data
116-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:55:9-57:60
117            android:name="com.facebook.sdk.ClientToken"
117-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:56:13-56
118            android:value="@string/facebook_client_token" />
118-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:57:13-58
119
120        <!-- Facebook Activity for Login -->
121        <activity
121-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:60:9-63:48
122            android:name="com.facebook.FacebookActivity"
122-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:61:13-57
123            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
123-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:62:13-96
124            android:label="@string/app_name"
124-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:63:13-45
125            android:theme="@style/com_facebook_activity_theme" />
125-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:23:13-63
126        <activity
126-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:64:9-73:20
127            android:name="com.facebook.CustomTabActivity"
127-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:65:13-58
128            android:exported="true" >
128-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:66:13-36
129            <intent-filter>
129-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:67:13-72:29
130                <action android:name="android.intent.action.VIEW" />
130-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:68:17-69
130-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:68:25-66
131
132                <category android:name="android.intent.category.DEFAULT" />
132-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:69:17-76
132-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:69:27-73
133                <category android:name="android.intent.category.BROWSABLE" />
133-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:70:17-78
133-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:70:27-75
134
135                <data android:scheme="@string/fb_login_protocol_scheme" />
135-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:71:17-75
135-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:71:23-72
136            </intent-filter>
137            <intent-filter>
137-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:29:13-38:29
138                <action android:name="android.intent.action.VIEW" />
138-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:68:17-69
138-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:68:25-66
139
140                <category android:name="android.intent.category.DEFAULT" />
140-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:69:17-76
140-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:69:27-73
141                <category android:name="android.intent.category.BROWSABLE" />
141-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:70:17-78
141-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:70:27-75
142
143                <data
143-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:71:17-75
144                    android:host="cct.com.velvete.ly"
145                    android:scheme="fbconnect" />
145-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:71:23-72
146            </intent-filter>
147        </activity>
148
149        <service
149-->[:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
150            android:name="com.baseflow.geolocator.GeolocatorLocationService"
150-->[:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
151            android:enabled="true"
151-->[:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
152            android:exported="false"
152-->[:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
153            android:foregroundServiceType="location" />
153-->[:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
154
155        <provider
155-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
156            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
156-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
157            android:authorities="com.velvete.ly.flutter.image_provider"
157-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
158            android:exported="false"
158-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
159            android:grantUriPermissions="true" >
159-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
160            <meta-data
160-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
161                android:name="android.support.FILE_PROVIDER_PATHS"
161-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
162                android:resource="@xml/flutter_image_picker_file_paths" />
162-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
163        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
164        <service
164-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
165            android:name="com.google.android.gms.metadata.ModuleDependencies"
165-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
166            android:enabled="false"
166-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
167            android:exported="false" >
167-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
168            <intent-filter>
168-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
169                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
169-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
169-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
170            </intent-filter>
171
172            <meta-data
172-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
173                android:name="photopicker_activity:0:required"
173-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
174                android:value="" />
174-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
175        </service>
176
177        <activity
177-->[:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
178            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
178-->[:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
179            android:exported="false"
179-->[:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
180            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
180-->[:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
181
182        <service
182-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
183            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
183-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
184            android:exported="false"
184-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
185            android:permission="android.permission.BIND_JOB_SERVICE" />
185-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
186        <service
186-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-24:19
187            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
187-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-97
188            android:exported="false" >
188-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
189            <intent-filter>
189-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
190                <action android:name="com.google.firebase.MESSAGING_EVENT" />
190-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
190-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
191            </intent-filter>
192        </service>
193
194        <receiver
194-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
195            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
195-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
196            android:exported="true"
196-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
197            android:permission="com.google.android.c2dm.permission.SEND" >
197-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
198            <intent-filter>
198-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
199                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
199-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
199-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
200            </intent-filter>
201        </receiver>
202
203        <service
203-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-39:19
204            android:name="com.google.firebase.components.ComponentDiscoveryService"
204-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:18-89
205            android:directBootAware="true"
205-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
206            android:exported="false" >
206-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:56:13-37
207            <meta-data
207-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
208                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
208-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
209                android:value="com.google.firebase.components.ComponentRegistrar" />
209-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
210            <meta-data
210-->[:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
211                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
211-->[:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
212                android:value="com.google.firebase.components.ComponentRegistrar" />
212-->[:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
213            <meta-data
213-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:57:13-59:85
214                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
214-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:58:17-122
215                android:value="com.google.firebase.components.ComponentRegistrar" />
215-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:59:17-82
216            <meta-data
216-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:60:13-62:85
217                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
217-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:61:17-119
218                android:value="com.google.firebase.components.ComponentRegistrar" />
218-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:62:17-82
219            <meta-data
219-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\599f7a4cda1d68eeb0a2af4979e75800\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
220                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
220-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\599f7a4cda1d68eeb0a2af4979e75800\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
221                android:value="com.google.firebase.components.ComponentRegistrar" />
221-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\599f7a4cda1d68eeb0a2af4979e75800\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
222            <meta-data
222-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\599f7a4cda1d68eeb0a2af4979e75800\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
223                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
223-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\599f7a4cda1d68eeb0a2af4979e75800\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
224                android:value="com.google.firebase.components.ComponentRegistrar" />
224-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\599f7a4cda1d68eeb0a2af4979e75800\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
225            <meta-data
225-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72b780c887663d459869298f44dc09c7\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
226                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
226-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72b780c887663d459869298f44dc09c7\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
227                android:value="com.google.firebase.components.ComponentRegistrar" />
227-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72b780c887663d459869298f44dc09c7\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
228            <meta-data
228-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
229                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
229-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
230                android:value="com.google.firebase.components.ComponentRegistrar" />
230-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
231            <meta-data
231-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5021680a46e39bb3bc3926e66e2ef48\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
232                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
232-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5021680a46e39bb3bc3926e66e2ef48\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
233                android:value="com.google.firebase.components.ComponentRegistrar" />
233-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5021680a46e39bb3bc3926e66e2ef48\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
234        </service>
235
236        <provider
236-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
237            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
237-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
238            android:authorities="com.velvete.ly.flutterfirebasemessaginginitprovider"
238-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
239            android:exported="false"
239-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
240            android:initOrder="99" />
240-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
241
242        <activity
242-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-18:47
243            android:name="com.pichillilorenzo.flutter_inappwebview_android.in_app_browser.InAppBrowserActivity"
243-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-112
244            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density"
244-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-137
245            android:exported="false"
245-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
246            android:theme="@style/AppTheme" />
246-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-44
247        <activity
247-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-22:55
248            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivity"
248-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-120
249            android:exported="false"
249-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-37
250            android:theme="@style/ThemeTransparent" />
250-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-52
251        <activity
251-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-26:55
252            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivity"
252-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-114
253            android:exported="false"
253-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-37
254            android:theme="@style/ThemeTransparent" />
254-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-52
255        <activity
255-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:9-31:55
256            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivitySingleInstance"
256-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-134
257            android:exported="false"
257-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-37
258            android:launchMode="singleInstance"
258-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-48
259            android:theme="@style/ThemeTransparent" />
259-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-52
260        <activity
260-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-36:55
261            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivitySingleInstance"
261-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-128
262            android:exported="false"
262-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-37
263            android:launchMode="singleInstance"
263-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-48
264            android:theme="@style/ThemeTransparent" />
264-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-52
265
266        <receiver
266-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:9-41:40
267            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ActionBroadcastReceiver"
267-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-119
268            android:enabled="true"
268-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-35
269            android:exported="false" />
269-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:13-37
270
271        <meta-data
271-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:9-45:36
272            android:name="io.flutter.embedded_views_preview"
272-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-61
273            android:value="true" />
273-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-33
274
275        <activity
275-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:21:9-65:20
276            android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetRedirectActivity"
276-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:22:13-109
277            android:exported="true"
277-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:23:13-36
278            android:launchMode="singleTask" >
278-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:24:13-44
279            <intent-filter>
279-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:25:13-64:29
280                <action android:name="android.intent.action.VIEW" />
280-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:68:17-69
280-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:68:25-66
281
282                <category android:name="android.intent.category.DEFAULT" />
282-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:69:17-76
282-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:69:27-73
283                <category android:name="android.intent.category.BROWSABLE" />
283-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:70:17-78
283-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:70:27-75
284
285                <!-- Returning from app2app: return_url is triggered to reopen web AuthFlow and poll accounts. -->
286                <data
286-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:71:17-75
287                    android:host="link-accounts"
288                    android:pathPrefix="/com.velvete.ly/authentication_return"
289                    android:scheme="stripe-auth" />
289-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:71:23-72
290
291                <!-- Returning from app2app: return_url is triggered to reopen native AuthFlow and poll accounts. -->
292                <data
292-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:71:17-75
293                    android:host="link-native-accounts"
294                    android:pathPrefix="/com.velvete.ly/authentication_return"
295                    android:scheme="stripe-auth" />
295-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:71:23-72
296
297                <!-- End of web AuthFlow success and cancel URIs that begin with "stripe-auth://link-accounts/{app-id}/...” -->
298                <data
298-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:71:17-75
299                    android:host="link-accounts"
300                    android:path="/com.velvete.ly/success"
301                    android:scheme="stripe-auth" />
301-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:71:23-72
302                <data
302-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:71:17-75
303                    android:host="link-accounts"
304                    android:path="/com.velvete.ly/cancel"
305                    android:scheme="stripe-auth" />
305-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:71:23-72
306
307                <!-- Opening app2app: Web flow triggers stripe-auth://native-redirect/{app-id}/http://web-that-redirects-to-native -->
308                <data
308-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:71:17-75
309                    android:host="native-redirect"
310                    android:pathPrefix="/com.velvete.ly"
311                    android:scheme="stripe-auth" />
311-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:71:23-72
312
313                <!-- Accepts success/cancel/fail URIs that begin with "stripe://auth-redirect” -->
314                <data
314-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:71:17-75
315                    android:host="auth-redirect"
316                    android:pathPrefix="/com.velvete.ly"
317                    android:scheme="stripe" />
317-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:71:23-72
318            </intent-filter>
319        </activity>
320        <activity
320-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:66:9-69:77
321            android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetActivity"
321-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:67:13-101
322            android:exported="false"
322-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:68:13-37
323            android:theme="@style/StripeFinancialConnectionsDefaultTheme" />
323-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:69:13-74
324        <activity
324-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:70:9-74:58
325            android:name="com.stripe.android.financialconnections.ui.FinancialConnectionsSheetNativeActivity"
325-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:71:13-110
326            android:exported="false"
326-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:72:13-37
327            android:theme="@style/StripeFinancialConnectionsDefaultTheme"
327-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:73:13-74
328            android:windowSoftInputMode="adjustResize" />
328-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3d2a3a73774752eda4e073ce287dde\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:74:13-55
329        <activity android:name="com.facebook.CustomTabMainActivity" />
329-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:24:9-71
329-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0d5f31597421a461151d738cf89ab978\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:24:19-68
330        <activity
330-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:8:9-11:69
331            android:name="com.stripe.android.paymentsheet.PaymentSheetActivity"
331-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:9:13-80
332            android:exported="false"
332-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:10:13-37
333            android:theme="@style/StripePaymentSheetDefaultTheme" />
333-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:11:13-66
334        <activity
334-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:12:9-15:69
335            android:name="com.stripe.android.paymentsheet.PaymentOptionsActivity"
335-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:13:13-82
336            android:exported="false"
336-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:14:13-37
337            android:theme="@style/StripePaymentSheetDefaultTheme" />
337-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:15:13-66
338        <activity
338-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:16:9-19:69
339            android:name="com.stripe.android.customersheet.CustomerSheetActivity"
339-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:17:13-82
340            android:exported="false"
340-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:18:13-37
341            android:theme="@style/StripePaymentSheetDefaultTheme" />
341-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:19:13-66
342        <activity
342-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:20:9-23:69
343            android:name="com.stripe.android.paymentsheet.addresselement.AddressElementActivity"
343-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:21:13-97
344            android:exported="false"
344-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:22:13-37
345            android:theme="@style/StripePaymentSheetDefaultTheme" />
345-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:23:13-66
346        <activity
346-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:24:9-27:69
347            android:name="com.stripe.android.paymentsheet.paymentdatacollection.bacs.BacsMandateConfirmationActivity"
347-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:25:13-118
348            android:exported="false"
348-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:26:13-37
349            android:theme="@style/StripePaymentSheetDefaultTheme" />
349-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:27:13-66
350        <activity
350-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:28:9-31:69
351            android:name="com.stripe.android.paymentsheet.paymentdatacollection.polling.PollingActivity"
351-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:29:13-105
352            android:exported="false"
352-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:30:13-37
353            android:theme="@style/StripePaymentSheetDefaultTheme" />
353-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:31:13-66
354        <activity
354-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:32:9-35:69
355            android:name="com.stripe.android.paymentsheet.ui.SepaMandateActivity"
355-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:33:13-82
356            android:exported="false"
356-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:34:13-37
357            android:theme="@style/StripePaymentSheetDefaultTheme" />
357-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:35:13-66
358        <activity
358-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:36:9-39:68
359            android:name="com.stripe.android.paymentsheet.ExternalPaymentMethodProxyActivity"
359-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:37:13-94
360            android:exported="false"
360-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:38:13-37
361            android:theme="@style/StripePayLauncherDefaultTheme" />
361-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:39:13-65
362        <activity
362-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:40:9-42:69
363            android:name="com.stripe.android.paymentsheet.paymentdatacollection.cvcrecollection.CvcRecollectionActivity"
363-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:41:13-121
364            android:theme="@style/StripePaymentSheetDefaultTheme" />
364-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:42:13-66
365        <activity
365-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:43:9-45:69
366            android:name="com.stripe.android.paymentelement.embedded.form.FormActivity"
366-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:44:13-88
367            android:theme="@style/StripePaymentSheetDefaultTheme" />
367-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:45:13-66
368        <activity
368-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:46:9-48:69
369            android:name="com.stripe.android.paymentelement.embedded.manage.ManageActivity"
369-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:47:13-92
370            android:theme="@style/StripePaymentSheetDefaultTheme" />
370-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:48:13-66
371        <activity
371-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:49:9-56:58
372            android:name="com.stripe.android.link.LinkActivity"
372-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:50:13-64
373            android:autoRemoveFromRecents="true"
373-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:51:13-49
374            android:configChanges="orientation|keyboard|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
374-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:52:13-115
375            android:exported="false"
375-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:53:13-37
376            android:label="@string/stripe_link"
376-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:54:13-48
377            android:theme="@style/StripeLinkBaseTheme"
377-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:55:13-55
378            android:windowSoftInputMode="adjustResize" />
378-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:56:13-55
379        <activity
379-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:57:9-62:61
380            android:name="com.stripe.android.link.LinkForegroundActivity"
380-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:58:13-74
381            android:autoRemoveFromRecents="true"
381-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:59:13-49
382            android:configChanges="orientation|keyboard|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
382-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:60:13-115
383            android:launchMode="singleTop"
383-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:61:13-43
384            android:theme="@style/StripeTransparentTheme" />
384-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:62:13-58
385        <activity
385-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:63:9-80:20
386            android:name="com.stripe.android.link.LinkRedirectHandlerActivity"
386-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:64:13-79
387            android:autoRemoveFromRecents="true"
387-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:65:13-49
388            android:exported="true"
388-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:66:13-36
389            android:launchMode="singleInstance"
389-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:67:13-48
390            android:theme="@style/StripeTransparentTheme" >
390-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:68:13-58
391            <intent-filter>
391-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a194771e56850ff6e97e94cf6f2b6b3b\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:69:13-79:29
392                <action android:name="android.intent.action.VIEW" />
392-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:68:17-69
392-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:68:25-66
393
394                <category android:name="android.intent.category.DEFAULT" />
394-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:69:17-76
394-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:69:27-73
395                <category android:name="android.intent.category.BROWSABLE" />
395-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:70:17-78
395-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:70:27-75
396
397                <data
397-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:71:17-75
398                    android:host="complete"
399                    android:path="/com.velvete.ly"
400                    android:scheme="link-popup" />
400-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:71:23-72
401            </intent-filter>
402        </activity>
403        <activity
403-->[com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a77bf49bd582bfd1c6ec842fafdd01ea\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:8:9-11:69
404            android:name="com.stripe.android.ui.core.cardscan.CardScanActivity"
404-->[com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a77bf49bd582bfd1c6ec842fafdd01ea\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:9:13-80
405            android:exported="false"
405-->[com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a77bf49bd582bfd1c6ec842fafdd01ea\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:10:13-37
406            android:theme="@style/StripePaymentSheetDefaultTheme" />
406-->[com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a77bf49bd582bfd1c6ec842fafdd01ea\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:11:13-66
407        <activity
407-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:15:9-18:57
408            android:name="com.stripe.android.view.PaymentAuthWebViewActivity"
408-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:16:13-78
409            android:exported="false"
409-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:17:13-37
410            android:theme="@style/StripeDefaultTheme" />
410-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:18:13-54
411        <activity
411-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:19:9-22:61
412            android:name="com.stripe.android.view.PaymentRelayActivity"
412-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:20:13-72
413            android:exported="false"
413-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:21:13-37
414            android:theme="@style/StripeTransparentTheme" />
414-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:22:13-58
415        <!--
416        Set android:launchMode="singleTop" so that the StripeBrowserLauncherActivity instance that
417        launched the browser Activity will also handle the return URL deep link.
418        -->
419        <activity
419-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:28:9-32:61
420            android:name="com.stripe.android.payments.StripeBrowserLauncherActivity"
420-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:29:13-85
421            android:exported="false"
421-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:30:13-37
422            android:launchMode="singleTask"
422-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:31:13-44
423            android:theme="@style/StripeTransparentTheme" />
423-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:32:13-58
424        <activity
424-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:33:9-50:20
425            android:name="com.stripe.android.payments.StripeBrowserProxyReturnActivity"
425-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:34:13-88
426            android:exported="true"
426-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:35:13-36
427            android:launchMode="singleTask"
427-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:36:13-44
428            android:theme="@style/StripeTransparentTheme" >
428-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:37:13-58
429            <intent-filter>
429-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:38:13-49:29
430                <action android:name="android.intent.action.VIEW" />
430-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:68:17-69
430-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:68:25-66
431
432                <category android:name="android.intent.category.DEFAULT" />
432-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:69:17-76
432-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:69:27-73
433                <category android:name="android.intent.category.BROWSABLE" />
433-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:70:17-78
433-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:70:27-75
434
435                <!-- Must match `DefaultReturnUrl#value`. -->
436                <data
436-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:71:17-75
437                    android:host="payment_return_url"
438                    android:path="/com.velvete.ly"
439                    android:scheme="stripesdk" />
439-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:71:23-72
440            </intent-filter>
441        </activity>
442        <activity
442-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:51:9-54:57
443            android:name="com.stripe.android.payments.core.authentication.threeds2.Stripe3ds2TransactionActivity"
443-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:52:13-114
444            android:exported="false"
444-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:53:13-37
445            android:theme="@style/StripeDefaultTheme" />
445-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:54:13-54
446        <activity
446-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:55:9-58:66
447            android:name="com.stripe.android.googlepaylauncher.GooglePayLauncherActivity"
447-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:56:13-90
448            android:exported="false"
448-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:57:13-37
449            android:theme="@style/StripeGooglePayDefaultTheme" />
449-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:58:13-63
450        <activity
450-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:59:9-62:66
451            android:name="com.stripe.android.googlepaylauncher.GooglePayPaymentMethodLauncherActivity"
451-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:60:13-103
452            android:exported="false"
452-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:61:13-37
453            android:theme="@style/StripeGooglePayDefaultTheme" />
453-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:62:13-63
454        <activity
454-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:63:9-66:68
455            android:name="com.stripe.android.payments.paymentlauncher.PaymentLauncherConfirmationActivity"
455-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:64:13-107
456            android:exported="false"
456-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:65:13-37
457            android:theme="@style/StripePayLauncherDefaultTheme" />
457-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:66:13-65
458        <activity
458-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:67:9-70:61
459            android:name="com.stripe.android.payments.bankaccount.ui.CollectBankAccountActivity"
459-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:68:13-97
460            android:exported="false"
460-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:69:13-37
461            android:theme="@style/StripeTransparentTheme" />
461-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b422652f078aeef4a5d67357b250b9f\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:70:13-58
462
463        <service
463-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:24:9-32:19
464            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
464-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:25:13-94
465            android:enabled="true"
465-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:26:13-35
466            android:exported="false" >
466-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:27:13-37
467            <meta-data
467-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:29:13-31:104
468                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
468-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:30:17-76
469                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
469-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:31:17-101
470        </service>
471
472        <activity
472-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:34:9-41:20
473            android:name="androidx.credentials.playservices.HiddenActivity"
473-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:35:13-76
474            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
474-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:36:13-87
475            android:enabled="true"
475-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:37:13-35
476            android:exported="false"
476-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:38:13-37
477            android:fitsSystemWindows="true"
477-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:39:13-45
478            android:theme="@style/Theme.Hidden" >
478-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:40:13-48
479        </activity>
480        <activity
480-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:42:9-49:20
481            android:name="androidx.credentials.playservices.IdentityCredentialApiHiddenActivity"
481-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:43:13-97
482            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
482-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:44:13-87
483            android:enabled="true"
483-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:45:13-35
484            android:exported="false"
484-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:46:13-37
485            android:fitsSystemWindows="true"
485-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:47:13-45
486            android:theme="@style/Theme.Hidden" >
486-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66defdccf03609952e58ee8f1d01f0df\transformed\jetified-credentials-play-services-auth-1.5.0\AndroidManifest.xml:48:13-48
487        </activity>
488        <activity
488-->[com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8057826fcdfd1bf2f63ffb4797b5d13\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:8:9-11:54
489            android:name="com.stripe.android.stripe3ds2.views.ChallengeActivity"
489-->[com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8057826fcdfd1bf2f63ffb4797b5d13\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:9:13-81
490            android:exported="false"
490-->[com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8057826fcdfd1bf2f63ffb4797b5d13\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:10:13-37
491            android:theme="@style/Stripe3DS2Theme" />
491-->[com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8057826fcdfd1bf2f63ffb4797b5d13\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:11:13-51
492        <activity
492-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
493            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
493-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
494            android:excludeFromRecents="true"
494-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
495            android:exported="false"
495-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
496            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
496-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
497        <!--
498            Service handling Google Sign-In user revocation. For apps that do not integrate with
499            Google Sign-In, this service will never be started.
500        -->
501        <service
501-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
502            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
502-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
503            android:exported="true"
503-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
504            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
504-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
505            android:visibleToInstantApps="true" />
505-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a4f7fea8597692fb5c34fbdf93d6a08\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
506        <!--
507         The initialization ContentProvider will call FacebookSdk.sdkInitialize automatically
508         with the application context. This config is merged in with the host app's manifest,
509         but there can only be one provider with the same authority activated at any given
510         point; so if the end user has two or more different apps that use Facebook SDK, only the
511         first one will be able to use the provider. To work around this problem, we use the
512         following placeholder in the authority to identify each host application as if it was
513         a completely different provider.
514        -->
515        <provider
515-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:32:9-35:40
516            android:name="com.facebook.internal.FacebookInitProvider"
516-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:33:13-70
517            android:authorities="com.velvete.ly.FacebookInitProvider"
517-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:34:13-72
518            android:exported="false" />
518-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:35:13-37
519
520        <receiver
520-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:37:9-43:20
521            android:name="com.facebook.CurrentAccessTokenExpirationBroadcastReceiver"
521-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:38:13-86
522            android:exported="false" >
522-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:39:13-37
523            <intent-filter>
523-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:40:13-42:29
524                <action android:name="com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED" />
524-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:41:17-95
524-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:41:25-92
525            </intent-filter>
526        </receiver>
527        <receiver
527-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:44:9-50:20
528            android:name="com.facebook.AuthenticationTokenManager$CurrentAuthenticationTokenChangedBroadcastReceiver"
528-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:45:13-118
529            android:exported="false" >
529-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:46:13-37
530            <intent-filter>
530-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:47:13-49:29
531                <action android:name="com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED" />
531-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:48:17-103
531-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3950e5dcaf0275b861085626f99d8b2e\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:48:25-100
532            </intent-filter>
533        </receiver>
534        <receiver
534-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:29:9-40:20
535            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
535-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:30:13-78
536            android:exported="true"
536-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:31:13-36
537            android:permission="com.google.android.c2dm.permission.SEND" >
537-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:32:13-73
538            <intent-filter>
538-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
539                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
539-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
539-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
540            </intent-filter>
541
542            <meta-data
542-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:37:13-39:40
543                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
543-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:38:17-92
544                android:value="true" />
544-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:39:17-37
545        </receiver>
546        <!--
547             FirebaseMessagingService performs security checks at runtime,
548             but set to not exported to explicitly avoid allowing another app to call it.
549        -->
550        <service
550-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:46:9-53:19
551            android:name="com.google.firebase.messaging.FirebaseMessagingService"
551-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:47:13-82
552            android:directBootAware="true"
552-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:48:13-43
553            android:exported="false" >
553-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01458e7795ca1352e7f49e1dfaec71b0\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:49:13-37
554            <intent-filter android:priority="-500" >
554-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
555                <action android:name="com.google.firebase.MESSAGING_EVENT" />
555-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
555-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
556            </intent-filter>
557        </service> <!-- Needs to be explicitly declared on P+ -->
558        <uses-library
558-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bda770351327ba3421df9753387b33\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
559            android:name="org.apache.http.legacy"
559-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bda770351327ba3421df9753387b33\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
560            android:required="false" />
560-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90bda770351327ba3421df9753387b33\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
561
562        <activity
562-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b8bc891082e16b1dbfe034ba3b1a5a9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
563            android:name="com.google.android.gms.common.api.GoogleApiActivity"
563-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b8bc891082e16b1dbfe034ba3b1a5a9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
564            android:exported="false"
564-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b8bc891082e16b1dbfe034ba3b1a5a9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
565            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
565-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b8bc891082e16b1dbfe034ba3b1a5a9\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
566
567        <provider
567-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
568            android:name="com.google.firebase.provider.FirebaseInitProvider"
568-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
569            android:authorities="com.velvete.ly.firebaseinitprovider"
569-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
570            android:directBootAware="true"
570-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
571            android:exported="false"
571-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
572            android:initOrder="100" />
572-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aa51e7a67c78b048063e285f7cc2f50\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
573
574        <uses-library
574-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ba767d3ac1f038378d1d0660d95028c\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
575            android:name="androidx.window.extensions"
575-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ba767d3ac1f038378d1d0660d95028c\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
576            android:required="false" />
576-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ba767d3ac1f038378d1d0660d95028c\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
577        <uses-library
577-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ba767d3ac1f038378d1d0660d95028c\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
578            android:name="androidx.window.sidecar"
578-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ba767d3ac1f038378d1d0660d95028c\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
579            android:required="false" />
579-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ba767d3ac1f038378d1d0660d95028c\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
580
581        <provider
581-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb65d20db0bdafbf79df86dc25eb4e50\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
582            android:name="androidx.startup.InitializationProvider"
582-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb65d20db0bdafbf79df86dc25eb4e50\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
583            android:authorities="com.velvete.ly.androidx-startup"
583-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb65d20db0bdafbf79df86dc25eb4e50\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
584            android:exported="false" >
584-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb65d20db0bdafbf79df86dc25eb4e50\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
585            <meta-data
585-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb65d20db0bdafbf79df86dc25eb4e50\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
586                android:name="androidx.emoji2.text.EmojiCompatInitializer"
586-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb65d20db0bdafbf79df86dc25eb4e50\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
587                android:value="androidx.startup" />
587-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb65d20db0bdafbf79df86dc25eb4e50\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
588            <meta-data
588-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80eb008a418d0155c58fd4d4c051c66e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
589                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
589-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80eb008a418d0155c58fd4d4c051c66e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
590                android:value="androidx.startup" />
590-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80eb008a418d0155c58fd4d4c051c66e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
591            <meta-data
591-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
592                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
592-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
593                android:value="androidx.startup" />
593-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
594        </provider>
595
596        <meta-data
596-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95d5c00a62ffa2a613f7134fa3c4f4ba\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
597            android:name="com.google.android.gms.version"
597-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95d5c00a62ffa2a613f7134fa3c4f4ba\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
598            android:value="@integer/google_play_services_version" />
598-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95d5c00a62ffa2a613f7134fa3c4f4ba\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
599
600        <receiver
600-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
601            android:name="androidx.profileinstaller.ProfileInstallReceiver"
601-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
602            android:directBootAware="false"
602-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
603            android:enabled="true"
603-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
604            android:exported="true"
604-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
605            android:permission="android.permission.DUMP" >
605-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
606            <intent-filter>
606-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
607                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
607-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
607-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
608            </intent-filter>
609            <intent-filter>
609-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
610                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
610-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
610-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
611            </intent-filter>
612            <intent-filter>
612-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
613                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
613-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
613-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
614            </intent-filter>
615            <intent-filter>
615-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
616                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
616-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
616-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa797ec5d6572429c7236788bc0d1ca0\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
617            </intent-filter>
618        </receiver>
619
620        <service
620-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700ec4c1038357bad82e4a8518ca93ac\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
621            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
621-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700ec4c1038357bad82e4a8518ca93ac\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
622            android:exported="false" >
622-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700ec4c1038357bad82e4a8518ca93ac\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
623            <meta-data
623-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700ec4c1038357bad82e4a8518ca93ac\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
624                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
624-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700ec4c1038357bad82e4a8518ca93ac\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
625                android:value="cct" />
625-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700ec4c1038357bad82e4a8518ca93ac\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
626        </service>
627        <service
627-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
628            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
628-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
629            android:exported="false"
629-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
630            android:permission="android.permission.BIND_JOB_SERVICE" >
630-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
631        </service>
632
633        <receiver
633-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
634            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
634-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
635            android:exported="false" />
635-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bf16caec370aee43a431dd2186f2f3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
636
637        <meta-data
637-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78656e3a4133de4686476170d28291c1\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:10:9-12:33
638            android:name="aia-compat-api-min-version"
638-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78656e3a4133de4686476170d28291c1\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:11:13-54
639            android:value="1" /> <!-- The activities will be merged into the manifest of the hosting app. -->
639-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78656e3a4133de4686476170d28291c1\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:12:13-30
640        <activity
640-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d57ee93d6de061edf1560f4205406b\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:14:9-18:65
641            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
641-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d57ee93d6de061edf1560f4205406b\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:15:13-93
642            android:exported="false"
642-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d57ee93d6de061edf1560f4205406b\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:16:13-37
643            android:stateNotNeeded="true"
643-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d57ee93d6de061edf1560f4205406b\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:17:13-42
644            android:theme="@style/Theme.PlayCore.Transparent" />
644-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20d57ee93d6de061edf1560f4205406b\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:18:13-62
645    </application>
646
647</manifest>
