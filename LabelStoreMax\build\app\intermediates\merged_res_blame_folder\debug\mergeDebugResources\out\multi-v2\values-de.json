{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-113:/values-de/values-de.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b05c15e4c638bbe5f218480c9cf74bd\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,197,264,349,418,479,551,618,682,750,820,880,942,1015,1079,1149,1212,1286,1349,1434,1511,1598,1691,1803,1891,1940,1988,2074,2136,2211,2280,2379,2467,2565", "endColumns": "64,76,66,84,68,60,71,66,63,67,69,59,61,72,63,69,62,73,62,84,76,86,92,111,87,48,47,85,61,74,68,98,87,97,93", "endOffsets": "115,192,259,344,413,474,546,613,677,745,815,875,937,1010,1074,1144,1207,1281,1344,1429,1506,1593,1686,1798,1886,1935,1983,2069,2131,2206,2275,2374,2462,2560,2654"}, "to": {"startLines": "219,222,225,227,228,229,236,237,239,240,241,242,243,244,245,247,248,251,262,263,275,277,278,312,313,327,339,340,342,349,350,360,361,369,370", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20674,20913,21173,21329,21414,21483,21984,22056,22201,22265,22333,22403,22463,22525,22598,22721,22791,23026,23944,24007,25107,25279,25366,29968,30080,31444,32351,32399,32538,33373,33448,34300,34399,35146,35244", "endColumns": "64,76,66,84,68,60,71,66,63,67,69,59,61,72,63,69,62,73,62,84,76,86,92,111,87,48,47,85,61,74,68,98,87,97,93", "endOffsets": "20734,20985,21235,21409,21478,21539,22051,22118,22260,22328,22398,22458,22520,22593,22657,22786,22849,23095,24002,24087,25179,25361,25454,30075,30163,31488,32394,32480,32595,33443,33512,34394,34482,35239,35333"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca6706086df4d54b31c3004876d79fd4\\transformed\\jetified-facebook-login-18.0.3\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,218,358,500,621,709,801,884,976,1072,1183,1292,1384,1476,1578,1695,1777,1860,2067,2163,2272,2383,2493", "endColumns": "162,139,141,120,87,91,82,91,95,110,108,91,91,101,116,81,82,206,95,108,110,109,137", "endOffsets": "213,353,495,616,704,796,879,971,1067,1178,1287,1379,1471,1573,1690,1772,1855,2062,2158,2267,2378,2488,2626"}, "to": {"startLines": "53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5019,5182,5322,5464,5585,5673,5765,5848,5940,6036,6147,6256,6348,6440,6542,6659,6741,6824,7031,7127,7236,7347,7457", "endColumns": "162,139,141,120,87,91,82,91,95,110,108,91,91,101,116,81,82,206,95,108,110,109,137", "endOffsets": "5177,5317,5459,5580,5668,5760,5843,5935,6031,6142,6251,6343,6435,6537,6654,6736,6819,7026,7122,7231,7342,7452,7590"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37a9eea61f7f246731189c96a915165d\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "91", "endOffsets": "142"}, "to": {"startLines": "354", "startColumns": "4", "startOffsets": "33724", "endColumns": "91", "endOffsets": "33811"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5b8bc891082e16b1dbfe034ba3b1a5a9\\transformed\\jetified-play-services-base-18.5.0\\res\\values-de\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,458,582,690,864,991,1108,1223,1399,1507,1672,1799,1957,2129,2196,2255", "endColumns": "104,159,123,107,173,126,116,114,175,107,164,126,157,171,66,58,75", "endOffsets": "297,457,581,689,863,990,1107,1222,1398,1506,1671,1798,1956,2128,2195,2254,2330"}, "to": {"startLines": "76,77,78,79,80,81,82,83,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7595,7704,7868,7996,8108,8286,8417,8538,8802,8982,9094,9263,9394,9556,9732,9803,9866", "endColumns": "108,163,127,111,177,130,120,118,179,111,168,130,161,175,70,62,79", "endOffsets": "7699,7863,7991,8103,8281,8412,8533,8652,8977,9089,9258,9389,9551,9727,9798,9861,9941"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a77bf49bd582bfd1c6ec842fafdd01ea\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,337,440,691,738,805,906,975,1265,1328,1426,1494,1549,1613,1691,1785,2107,2183,2247,2300,2353,2444,2575,2691,2748,2837,2918,3004,3071,3177,3533,3613,3690,3753,3813,3876,3936,4010,4093,4189,4288,4371,4477,4577,4651,4730,4821,5060,5308,5425,5555,5614,6420,6524,6589", "endColumns": "119,161,102,250,46,66,100,68,289,62,97,67,54,63,77,93,321,75,63,52,52,90,130,115,56,88,80,85,66,105,355,79,76,62,59,62,59,73,82,95,98,82,105,99,73,78,90,238,247,116,129,58,805,103,64,54", "endOffsets": "170,332,435,686,733,800,901,970,1260,1323,1421,1489,1544,1608,1686,1780,2102,2178,2242,2295,2348,2439,2570,2686,2743,2832,2913,2999,3066,3172,3528,3608,3685,3748,3808,3871,3931,4005,4088,4184,4283,4366,4472,4572,4646,4725,4816,5055,5303,5420,5550,5609,6415,6519,6584,6639"}, "to": {"startLines": "281,282,283,285,289,290,291,292,293,294,295,311,314,316,320,321,326,332,333,341,351,355,356,357,358,359,365,368,373,375,376,377,378,381,383,384,388,392,393,395,397,409,434,435,436,437,438,456,463,464,465,466,468,469,470,487", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "25656,25776,25938,26141,27247,27294,27361,27462,27531,27821,27884,29900,30168,30369,30668,30746,31122,31855,31931,32485,33517,33816,33907,34038,34154,34211,34806,35060,35504,35650,35756,36112,36192,36443,36652,36712,37019,37652,37726,37902,38104,39858,42343,42449,42549,42623,42702,44406,45152,45400,45517,45647,45775,46581,46685,48595", "endColumns": "119,161,102,250,46,66,100,68,289,62,97,67,54,63,77,93,321,75,63,52,52,90,130,115,56,88,80,85,66,105,355,79,76,62,59,62,59,73,82,95,98,82,105,99,73,78,90,238,247,116,129,58,805,103,64,54", "endOffsets": "25771,25933,26036,26387,27289,27356,27457,27526,27816,27879,27977,29963,30218,30428,30741,30835,31439,31926,31990,32533,33565,33902,34033,34149,34206,34295,34882,35141,35566,35751,36107,36187,36264,36501,36707,36770,37074,37721,37804,37993,38198,39936,42444,42544,42618,42697,42788,44640,45395,45512,45642,45701,46576,46680,46745,48645"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\190cb7abb318e85f1c79a4fa923f65ed\\transformed\\appcompat-1.7.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,612,727,805,880,972,1066,1162,1263,1370,1470,1574,1672,1770,1867,1949,2060,2162,2260,2367,2470,2574,2730,2832", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "205,303,415,501,607,722,800,875,967,1061,1157,1258,1365,1465,1569,1667,1765,1862,1944,2055,2157,2255,2362,2465,2569,2725,2827,2909"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,438,536,648,734,840,955,1033,1108,1200,1294,1390,1491,1598,1698,1802,1900,1998,2095,2177,2288,2390,2488,2595,2698,2802,2958,18589", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "433,531,643,729,835,950,1028,1103,1195,1289,1385,1486,1593,1693,1797,1895,1993,2090,2172,2283,2385,2483,2590,2693,2797,2953,3055,18666"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57d1206001d3e5c2badfd9d4afb4491b\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-de\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,73", "endOffsets": "258,332"}, "to": {"startLines": "120,527", "startColumns": "4,4", "startOffsets": "12753,52448", "endColumns": "60,77", "endOffsets": "12809,52521"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\732d08034aa94dff057adb759b93ca56\\transformed\\jetified-foundation-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,89", "endOffsets": "137,227"}, "to": {"startLines": "523,524", "startColumns": "4,4", "startOffsets": "52115,52202", "endColumns": "86,89", "endOffsets": "52197,52287"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c3c723f32cc31c3d5ce9263682ffa8b7\\transformed\\browser-1.8.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,260,371", "endColumns": "103,100,110,99", "endOffsets": "154,255,366,466"}, "to": {"startLines": "96,107,108,109", "startColumns": "4,4,4,4", "startOffsets": "10127,11074,11175,11286", "endColumns": "103,100,110,99", "endOffsets": "10226,11170,11281,11381"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a194771e56850ff6e97e94cf6f2b6b3b\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,218,299,399,489,567,644,790,890,1002,1101,1234,1295,1441,1508,1700,1908,2081,2174,2280,2377,2473,2669,2775,2871,3129,3245,3383,3601,3832,3935,4062,4274,4371,4435,4502,4593,4689,4780,4856,4954,5047,5150,5394,5466,5534,5610,5731,5808,5897,5982,6071,6143,6212,6337,6430,6534,6628,6704,6787,6858,6948,7011,7116,7234,7342,7472,7538,7634,7735,7878,7950,8042,8149,8208,8273,8359,8468,8658,8976,9311,9410,9496,9580,9653,9770,9907,9998,10073,10151,10246,10329,10421,10562,10714,10782,10856,10915,11099,11167,11226,11304,11378,11456,11544,11689,11828,11952,12044,12124,12210,12284", "endColumns": "72,89,80,99,89,77,76,145,99,111,98,132,60,145,66,191,207,172,92,105,96,95,195,105,95,257,115,137,217,230,102,126,211,96,63,66,90,95,90,75,97,92,102,243,71,67,75,120,76,88,84,88,71,68,124,92,103,93,75,82,70,89,62,104,117,107,129,65,95,100,142,71,91,106,58,64,85,108,189,317,334,98,85,83,72,116,136,90,74,77,94,82,91,140,151,67,73,58,183,67,58,77,73,77,87,144,138,123,91,79,85,73,166", "endOffsets": "123,213,294,394,484,562,639,785,885,997,1096,1229,1290,1436,1503,1695,1903,2076,2169,2275,2372,2468,2664,2770,2866,3124,3240,3378,3596,3827,3930,4057,4269,4366,4430,4497,4588,4684,4775,4851,4949,5042,5145,5389,5461,5529,5605,5726,5803,5892,5977,6066,6138,6207,6332,6425,6529,6623,6699,6782,6853,6943,7006,7111,7229,7337,7467,7533,7629,7730,7873,7945,8037,8144,8203,8268,8354,8463,8653,8971,9306,9405,9491,9575,9648,9765,9902,9993,10068,10146,10241,10324,10416,10557,10709,10777,10851,10910,11094,11162,11221,11299,11373,11451,11539,11684,11823,11947,12039,12119,12205,12279,12446"}, "to": {"startLines": "212,213,214,284,296,297,298,315,328,330,331,362,380,382,385,389,390,391,394,396,398,399,400,401,402,403,404,405,406,407,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,457,461,471,472,473,474,475,476,477,478,479,488,489,490,491,492,493,494,495,496,497,498,499,500,501,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20117,20190,20280,26041,27982,28072,28150,30223,31493,31644,31756,34487,36382,36506,36775,37079,37271,37479,37809,37998,38203,38300,38396,38592,38698,38794,39052,39168,39306,39524,39755,39941,40068,40280,40377,40441,40508,40599,40695,40786,40862,40960,41053,41156,41400,41472,41540,41616,41737,41814,41903,41988,42077,42149,42218,42793,42886,42990,43084,43160,43243,43314,43404,43467,43572,43690,43798,43928,43994,44090,44191,44334,44645,44990,46750,46809,46874,46960,47069,47259,47577,47912,48011,48650,48734,48807,48924,49061,49152,49227,49305,49400,49483,49575,49716,49868,49936,50086,50145,50329,50397,50456,50534,50608,50686,50774,50919,51058,51182,51274,51354,51440,51514", "endColumns": "72,89,80,99,89,77,76,145,99,111,98,132,60,145,66,191,207,172,92,105,96,95,195,105,95,257,115,137,217,230,102,126,211,96,63,66,90,95,90,75,97,92,102,243,71,67,75,120,76,88,84,88,71,68,124,92,103,93,75,82,70,89,62,104,117,107,129,65,95,100,142,71,91,106,58,64,85,108,189,317,334,98,85,83,72,116,136,90,74,77,94,82,91,140,151,67,73,58,183,67,58,77,73,77,87,144,138,123,91,79,85,73,166", "endOffsets": "20185,20275,20356,26136,28067,28145,28222,30364,31588,31751,31850,34615,36438,36647,36837,37266,37474,37647,37897,38099,38295,38391,38587,38693,38789,39047,39163,39301,39519,39750,39853,40063,40275,40372,40436,40503,40594,40690,40781,40857,40955,41048,41151,41395,41467,41535,41611,41732,41809,41898,41983,42072,42144,42213,42338,42881,42985,43079,43155,43238,43309,43399,43462,43567,43685,43793,43923,43989,44085,44186,44329,44401,44732,45092,46804,46869,46955,47064,47254,47572,47907,48006,48092,48729,48802,48919,49056,49147,49222,49300,49395,49478,49570,49711,49863,49931,50005,50140,50324,50392,50451,50529,50603,50681,50769,50914,51053,51177,51269,51349,51435,51509,51676"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\587767e501a9ab66a3f91617d285250f\\transformed\\core-1.16.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,455,563,668,786", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "148,250,350,450,558,663,781,882"}, "to": {"startLines": "40,41,42,43,44,45,46,197", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3722,3820,3922,4022,4122,4230,4335,18982", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "3815,3917,4017,4117,4225,4330,4448,19078"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ff12406a05f415564f57bbfef1a99d3\\transformed\\jetified-ui-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,289,387,487,574,659,751,840,928,993,1057,1138,1222,1297,1376,1442", "endColumns": "95,87,97,99,86,84,91,88,87,64,63,80,83,74,78,65,119", "endOffsets": "196,284,382,482,569,654,746,835,923,988,1052,1133,1217,1292,1371,1437,1557"}, "to": {"startLines": "50,51,97,99,101,122,123,183,184,186,187,190,191,195,520,521,522", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4757,4853,10231,10424,10600,12884,12969,17813,17902,18073,18138,18424,18505,18831,51850,51929,51995", "endColumns": "95,87,97,99,86,84,91,88,87,64,63,80,83,74,78,65,119", "endOffsets": "4848,4936,10324,10519,10682,12964,13056,17897,17985,18133,18197,18500,18584,18901,51924,51990,52110"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95d5c00a62ffa2a613f7134fa3c4f4ba\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-de\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "84", "startColumns": "4", "startOffsets": "8657", "endColumns": "144", "endOffsets": "8797"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e3c1b17a9ffaafd0471e6527f4794f6b\\transformed\\jetified-credentials-1.5.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,113", "endOffsets": "162,276"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3060,3172", "endColumns": "111,113", "endOffsets": "3167,3281"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b422652f078aeef4a5d67357b250b9f\\transformed\\jetified-payments-core-21.6.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,204,273,342,420,482,541,597,684,766,854,951,1048,1134,1223,1301,1390,1486,1580,1651,1742,1827,1916,2029,2115,2206,2284,2382,2471,2575,2664,2755,2849,2974,3059,3154,3256,3351,3436,3501,4206,4899,4973,5103,5209,5264,5372,5472,5540,5634,5734,5791,5879,5931,6011,6114,6191,6263,6330,6396,6447,6528,6623,6705,6752,6803,6878,6936,6997,7217,7417,7576,7641,7730,7820,7916,8002,8089,8166,8255,8334,8447,8534,8624,8680,8825,8877,8932,9001,9070,9145,9209,9281,9370,9443,9499", "endColumns": "69,78,68,68,77,61,58,55,86,81,87,96,96,85,88,77,88,95,93,70,90,84,88,112,85,90,77,97,88,103,88,90,93,124,84,94,101,94,84,64,704,692,73,129,105,54,107,99,67,93,99,56,87,51,79,102,76,71,66,65,50,80,94,81,46,50,74,57,60,219,199,158,64,88,89,95,85,86,76,88,78,112,86,89,55,144,51,54,68,68,74,63,71,88,72,55,75", "endOffsets": "120,199,268,337,415,477,536,592,679,761,849,946,1043,1129,1218,1296,1385,1481,1575,1646,1737,1822,1911,2024,2110,2201,2279,2377,2466,2570,2659,2750,2844,2969,3054,3149,3251,3346,3431,3496,4201,4894,4968,5098,5204,5259,5367,5467,5535,5629,5729,5786,5874,5926,6006,6109,6186,6258,6325,6391,6442,6523,6618,6700,6747,6798,6873,6931,6992,7212,7412,7571,7636,7725,7815,7911,7997,8084,8161,8250,8329,8442,8529,8619,8675,8820,8872,8927,8996,9065,9140,9204,9276,9365,9438,9494,9570"}, "to": {"startLines": "205,206,207,208,209,210,211,215,216,217,218,221,223,224,226,231,235,250,253,254,255,257,258,259,261,265,266,267,268,269,270,271,272,273,274,276,279,280,286,287,288,299,300,301,302,303,304,305,306,307,308,309,310,317,318,319,322,323,324,325,329,334,335,336,337,338,343,344,345,346,347,348,352,353,363,364,366,367,371,372,374,379,386,387,458,459,460,462,467,480,481,482,483,484,485,486,502", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19631,19701,19780,19849,19918,19996,20058,20361,20417,20504,20586,20816,20990,21087,21240,21602,21895,22930,23174,23268,23339,23505,23590,23679,23858,24163,24254,24332,24430,24519,24623,24712,24803,24897,25022,25184,25459,25561,26392,26477,26542,28227,28920,28994,29124,29230,29285,29393,29493,29561,29655,29755,29812,30433,30485,30565,30840,30917,30989,31056,31593,31995,32076,32171,32253,32300,32600,32675,32733,32794,33014,33214,33570,33635,34620,34710,34887,34973,35338,35415,35571,36269,36842,36929,44737,44793,44938,45097,45706,48097,48166,48241,48305,48377,48466,48539,50010", "endColumns": "69,78,68,68,77,61,58,55,86,81,87,96,96,85,88,77,88,95,93,70,90,84,88,112,85,90,77,97,88,103,88,90,93,124,84,94,101,94,84,64,704,692,73,129,105,54,107,99,67,93,99,56,87,51,79,102,76,71,66,65,50,80,94,81,46,50,74,57,60,219,199,158,64,88,89,95,85,86,76,88,78,112,86,89,55,144,51,54,68,68,74,63,71,88,72,55,75", "endOffsets": "19696,19775,19844,19913,19991,20053,20112,20412,20499,20581,20669,20908,21082,21168,21324,21675,21979,23021,23263,23334,23425,23585,23674,23787,23939,24249,24327,24425,24514,24618,24707,24798,24892,25017,25102,25274,25556,25651,26472,26537,27242,28915,28989,29119,29225,29280,29388,29488,29556,29650,29750,29807,29895,30480,30560,30663,30912,30984,31051,31117,31639,32071,32166,32248,32295,32346,32670,32728,32789,33009,33209,33368,33630,33719,34705,34801,34968,35055,35410,35499,35645,36377,36924,37014,44788,44933,44985,45147,45770,48161,48236,48300,48372,48461,48534,48590,50081"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a0dab8dcd7d2485eaa7e5db7dd4cb3a3\\transformed\\jetified-stripe-core-21.6.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,190,252,336,405,483,542,618,692,767,833", "endColumns": "76,57,61,83,68,77,58,75,73,74,65,70", "endOffsets": "127,185,247,331,400,478,537,613,687,762,828,899"}, "to": {"startLines": "220,230,232,233,234,238,246,249,252,256,260,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20739,21544,21680,21742,21826,22123,22662,22854,23100,23430,23792,24092", "endColumns": "76,57,61,83,68,77,58,75,73,74,65,70", "endOffsets": "20811,21597,21737,21821,21890,22196,22716,22925,23169,23500,23853,24158"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be2b43e4377e03d598e671e01a23c196\\transformed\\material-1.12.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,374,463,547,637,719,820,942,1023,1085,1151,1245,1315,1374,1482,1548,1617,1675,1747,1811,1865,1993,2053,2115,2169,2247,2384,2476,2554,2648,2734,2818,2963,3047,3133,3266,3356,3435,3492,3543,3609,3683,3765,3836,3911,3985,4063,4135,4209,4319,4411,4493,4582,4671,4745,4823,4909,4964,5043,5110,5190,5274,5336,5400,5463,5532,5639,5746,5845,5951,6012,6067,6149,6232,6309", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,90,88,83,89,81,100,121,80,61,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,77,93,85,83,144,83,85,132,89,78,56,50,65,73,81,70,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,60,54,81,82,76,75", "endOffsets": "278,369,458,542,632,714,815,937,1018,1080,1146,1240,1310,1369,1477,1543,1612,1670,1742,1806,1860,1988,2048,2110,2164,2242,2379,2471,2549,2643,2729,2813,2958,3042,3128,3261,3351,3430,3487,3538,3604,3678,3760,3831,3906,3980,4058,4130,4204,4314,4406,4488,4577,4666,4740,4818,4904,4959,5038,5105,5185,5269,5331,5395,5458,5527,5634,5741,5840,5946,6007,6062,6144,6227,6304,6380"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3286,3377,3466,3550,3640,4453,4554,4676,10687,10749,10980,12814,13061,13120,13228,13294,13363,13421,13493,13557,13611,13739,13799,13861,13915,13993,14130,14222,14300,14394,14480,14564,14709,14793,14879,15012,15102,15181,15238,15289,15355,15429,15511,15582,15657,15731,15809,15881,15955,16065,16157,16239,16328,16417,16491,16569,16655,16710,16789,16856,16936,17020,17082,17146,17209,17278,17385,17492,17591,17697,17758,18202,18671,18754,18906", "endLines": "5,35,36,37,38,39,47,48,49,102,103,106,121,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,188,193,194,196", "endColumns": "12,90,88,83,89,81,100,121,80,61,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,77,93,85,83,144,83,85,132,89,78,56,50,65,73,81,70,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,60,54,81,82,76,75", "endOffsets": "328,3372,3461,3545,3635,3717,4549,4671,4752,10744,10810,11069,12879,13115,13223,13289,13358,13416,13488,13552,13606,13734,13794,13856,13910,13988,14125,14217,14295,14389,14475,14559,14704,14788,14874,15007,15097,15176,15233,15284,15350,15424,15506,15577,15652,15726,15804,15876,15950,16060,16152,16234,16323,16412,16486,16564,16650,16705,16784,16851,16931,17015,17077,17141,17204,17273,17380,17487,17586,17692,17753,17808,18279,18749,18826,18977"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c8057826fcdfd1bf2f63ffb4797b5d13\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,145,247,315,382,449,529", "endColumns": "89,101,67,66,66,79,73", "endOffsets": "140,242,310,377,444,524,598"}, "to": {"startLines": "198,199,200,201,202,203,204", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "19083,19173,19275,19343,19410,19477,19557", "endColumns": "89,101,67,66,66,79,73", "endOffsets": "19168,19270,19338,19405,19472,19552,19626"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\97b0e46e5034b62169defac2cb4fe8fb\\transformed\\preference-1.2.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,265,348,488,657,737", "endColumns": "71,87,82,139,168,79,75", "endOffsets": "172,260,343,483,652,732,808"}, "to": {"startLines": "95,104,185,189,519,525,526", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "10055,10815,17990,18284,51681,52292,52372", "endColumns": "71,87,82,139,168,79,75", "endOffsets": "10122,10898,18068,18419,51845,52367,52443"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\af486666b169eb5d50216ab5d4cc9553\\transformed\\biometric-1.1.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,259,378,510,645,786,908,1065,1164,1310,1498", "endColumns": "108,94,118,131,134,140,121,156,98,145,187,127", "endOffsets": "159,254,373,505,640,781,903,1060,1159,1305,1493,1621"}, "to": {"startLines": "94,98,110,111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9946,10329,11386,11505,11637,11772,11913,12035,12192,12291,12437,12625", "endColumns": "108,94,118,131,134,140,121,156,98,145,187,127", "endOffsets": "10050,10419,11500,11632,11767,11908,12030,12187,12286,12432,12620,12748"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb54734c218ce176bffcbd48481aca79\\transformed\\jetified-material3-1.0.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,209", "endColumns": "77,75,76", "endOffsets": "128,204,281"}, "to": {"startLines": "52,100,105", "startColumns": "4,4,4", "startOffsets": "4941,10524,10903", "endColumns": "77,75,76", "endOffsets": "5014,10595,10975"}}]}]}