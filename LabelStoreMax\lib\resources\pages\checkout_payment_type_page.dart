//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/app/models/checkout_session.dart';
import '/app/models/payment_type.dart';
import '/bootstrap/helpers.dart' as helpers;
import '/resources/widgets/safearea_widget.dart';

import 'package:nylo_framework/nylo_framework.dart';

class CheckoutPaymentTypePage extends NyStatefulWidget {
  static RouteView path =
      ("/checkout-payment-type", (_) => CheckoutPaymentTypePage());

  CheckoutPaymentTypePage({super.key})
      : super(child: () => _CheckoutPaymentTypePageState());
}

class _CheckoutPaymentTypePageState extends NyPage<CheckoutPaymentTypePage> {
  List<PaymentType?> _paymentTypes = [];

  @override
  get init => () async {
        super.init();

        _paymentTypes = await helpers.getPaymentTypes();

        if (_paymentTypes.isEmpty &&
            getEnv('APP_DEBUG', defaultValue: false) == true) {
          NyLogger.info(
              'You have no payment methods set. Visit the WooSignal dashboard (https://woosignal.com/dashboard) to set a payment method.');
        }

        if (CheckoutSession.getInstance.paymentType == null) {
          if (_paymentTypes.isNotEmpty) {
            CheckoutSession.getInstance.paymentType = _paymentTypes.firstWhere(
                (paymentType) => paymentType?.id == 20,
                orElse: () => _paymentTypes.first);
          }
        }
      };

  @override
  Widget view(BuildContext context) {
    return Scaffold(
      backgroundColor: helpers.ThemeColor.get(context).backgroundContainer, // FIX: Use themed color
      appBar: AppBar(
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        elevation: 0,
        title: Text(
          "طريقة الدفع",
          style: TextStyle(
            color: helpers.ThemeColor.get(context).textPrimary, // FIX: Use themed color
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: helpers.ThemeColor.get(context).textPrimary), // FIX: Use themed color
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SafeAreaWidget(
        child: _buildBeautifulPaymentPage(),
      ),
    );
  }

  /// Build the beautiful payment page matching the example image
  Widget _buildBeautifulPaymentPage() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(20),
      child: Column(
        children: [
          // Top illustration section
          Container(
            padding: EdgeInsets.all(20),
            margin: EdgeInsets.only(bottom: 30),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: helpers.ThemeColor.get(context).shadowLight,
                  blurRadius: 10,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Cash on delivery illustration
                Container(
                  height: 140,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: helpers.ThemeColor.get(context).backgroundContainer, // FIX: Use themed color
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Center(
                    child: Image.asset(
                      'public/images/payment_page/psyment page illutration.png',
                      height: 100,
                      fit: BoxFit.contain,
                      errorBuilder: (context, error, stackTrace) => Icon(
                        Icons.payments_outlined,
                        size: 50,
                        color: helpers.ThemeColor.get(context).brandPrimary,
                      ),
                    ),
                  ),
                ),
                SizedBox(height: 20),

                // Content section
                Text(
                  'اختر طريقة الدفع',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: helpers.ThemeColor.get(context).textPrimary, // FIX: Use themed color
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 8),
                Text(
                  'حدد الطريقة المناسبة لك',
                  style: TextStyle(
                    fontSize: 14,
                    color: ThemeColor.get(context).textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          // Payment method card
          _buildPaymentMethodCard(),

          SizedBox(height: 30),

          // Cancel button
          _buildCancelButton(),
        ],
      ),
    );
  }



  /// Build the payment method card matching the example design
  Widget _buildPaymentMethodCard() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: helpers.ThemeColor.get(context).shadowMedium,
            blurRadius: 20,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: _paymentTypes.isEmpty ? _buildNoPaymentMethods() : _buildPaymentMethodsList(),
    );
  }

  /// Build no payment methods message
  Widget _buildNoPaymentMethods() {
    return Column(
      children: [
        // Info icon
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: ThemeColor.get(context).brandSecondary.withValues(alpha: 0.3), // FIX: Use themed color
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.payment,
            color: ThemeColor.get(context).brandPrimary, // FIX: Use themed color
            size: 30,
          ),
        ),
        SizedBox(height: 20),

        // Message
        Text(
          "لا توجد طرق دفع متاحة",
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: ThemeColor.get(context).textPrimary, // FIX: Use themed color
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 8),

        Text(
          "No payment methods are available",
          style: TextStyle(
            fontSize: 14,
            color: ThemeColor.get(context).textSecondary, // FIX: Use themed color
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// Build payment methods list
  Widget _buildPaymentMethodsList() {
    return Column(
      children: [
        // Green checkmark
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: helpers.ThemeColor.get(context).successColor, // FIX: Use themed color
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.check,
            color: helpers.ThemeColor.get(context).textOnPrimary,
            size: 30,
          ),
        ),
        SizedBox(height: 20),

        // Cash on delivery title
        Text(
          "الدفع كاش عند الاستلام",
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: helpers.ThemeColor.get(context).textPrimary, // FIX: Use themed color
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 16),

        // Dynamic description - only show for Cash on Delivery when selected
        if (CheckoutSession.getInstance.paymentType?.name == "CashOnDelivery") ...[
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: helpers.ThemeColor.get(context).backgroundContainer, // FIX: Use themed color
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: helpers.ThemeColor.get(context).borderSecondary), // FIX: Use themed color
            ),
            child: Text(
              "نفخر بسري بالطاقة لكن المندوب\nمش حيكون معاه ماكينة الدفع\nPOS ، المعاملة تكون أونلاين من\nهاتفك \"تحويل\" يعني البطاقة لازم\nتكون مفعلة عالشراء أونلاين",
              style: TextStyle(
                fontSize: 14,
                color: helpers.ThemeColor.get(context).textSecondary, // FIX: Use themed color
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox(height: 20),
        ],

        // Payment methods selection
        ..._paymentTypes.map((paymentType) => _buildPaymentMethodItem(paymentType!)).toList(),
      ],
    );
  }

  /// Build individual payment method item
  Widget _buildPaymentMethodItem(PaymentType paymentType) {
    bool isSelected = CheckoutSession.getInstance.paymentType?.id == paymentType.id;

    return Container(
      margin: EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: isSelected ? ThemeColor.get(context).successColor.withValues(alpha: 0.1) : ThemeColor.get(context).backgroundContainer, // FIX: Use themed colors
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected ? ThemeColor.get(context).successColor : ThemeColor.get(context).borderSecondary, // FIX: Use themed colors
          width: isSelected ? 2 : 1,
        ),
      ),
      child: ListTile(
        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: ThemeColor.get(context).surfaceElevated,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: ThemeColor.get(context).shadowLight,
                blurRadius: 4,
                offset: Offset(0, 2),
              ),
            ],
          ),
          padding: EdgeInsets.all(8),
          child: paymentType.assetImage.startsWith('http')
            ? Image.network(
                paymentType.assetImage,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) => Icon(Icons.payment, color: helpers.ThemeColor.get(context).brandPrimary), // FIX: Use themed color
              )
            : Image.asset(
                helpers.getImageAsset(paymentType.assetImage),
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) => Icon(Icons.payment, color: helpers.ThemeColor.get(context).brandPrimary), // FIX: Use themed color
              ),
        ),
        title: Text(
          paymentType.desc,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: helpers.ThemeColor.get(context).textPrimary, // FIX: Use themed color
          ),
        ),
        trailing: isSelected
          ? Icon(Icons.check_circle, color: ThemeColor.get(context).successColor, size: 24) // FIX: Use themed color
          : Icon(Icons.radio_button_unchecked, color: ThemeColor.get(context).textMuted, size: 24), // FIX: Use themed color
        onTap: () {
          setState(() {
            CheckoutSession.getInstance.paymentType = paymentType;
          });
          Navigator.pop(context);
        },
      ),
    );
  }

  /// Build cancel button
  Widget _buildCancelButton() {
    return Container(
      width: double.infinity,
      height: 50,
      child: OutlinedButton(
        onPressed: () => Navigator.pop(context),
        style: OutlinedButton.styleFrom(
          side: BorderSide(color: ThemeColor.get(context).brandPrimary, width: 2), // FIX: Use themed color
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Text(
          "إلغاء",
          style: TextStyle(
            color: ThemeColor.get(context).brandPrimary, // FIX: Use themed color
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}
